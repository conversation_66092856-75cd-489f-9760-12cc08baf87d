import { createTheme } from '@mui/material/styles';

// Color palette for health app
const colors = {
  primary: {
    main: '#2E7D32', // Medical green
    light: '#4CAF50',
    dark: '#1B5E20',
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#1976D2', // Trust blue
    light: '#42A5F5',
    dark: '#0D47A1',
    contrastText: '#ffffff',
  },
  success: {
    main: '#4CAF50',
    light: '#81C784',
    dark: '#388E3C',
  },
  warning: {
    main: '#FF9800',
    light: '#FFB74D',
    dark: '#F57C00',
  },
  error: {
    main: '#F44336',
    light: '#EF5350',
    dark: '#C62828',
  },
  info: {
    main: '#2196F3',
    light: '#64B5F6',
    dark: '#1976D2',
  },
  background: {
    default: '#F8F9FA',
    paper: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
  },
  grey: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
};

// Custom health-related colors
const healthColors = {
  bloodPressure: {
    normal: '#4CAF50',
    elevated: '#FF9800',
    high: '#F44336',
  },
  bloodSugar: {
    normal: '#4CAF50',
    prediabetic: '#FF9800',
    diabetic: '#F44336',
  },
  bmi: {
    underweight: '#2196F3',
    normal: '#4CAF50',
    overweight: '#FF9800',
    obese: '#F44336',
  },
  riskLevel: {
    low: '#4CAF50',
    medium: '#FF9800',
    high: '#F44336',
    critical: '#D32F2F',
  },
};

// Typography configuration
const typography = {
  fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  h1: {
    fontSize: '2.5rem',
    fontWeight: 700,
    lineHeight: 1.2,
  },
  h2: {
    fontSize: '2rem',
    fontWeight: 600,
    lineHeight: 1.3,
  },
  h3: {
    fontSize: '1.75rem',
    fontWeight: 600,
    lineHeight: 1.3,
  },
  h4: {
    fontSize: '1.5rem',
    fontWeight: 600,
    lineHeight: 1.4,
  },
  h5: {
    fontSize: '1.25rem',
    fontWeight: 600,
    lineHeight: 1.4,
  },
  h6: {
    fontSize: '1.125rem',
    fontWeight: 600,
    lineHeight: 1.4,
  },
  body1: {
    fontSize: '1rem',
    lineHeight: 1.5,
  },
  body2: {
    fontSize: '0.875rem',
    lineHeight: 1.5,
  },
  button: {
    fontSize: '0.875rem',
    fontWeight: 600,
    textTransform: 'none' as const,
  },
  caption: {
    fontSize: '0.75rem',
    lineHeight: 1.4,
  },
};

// Component overrides
const components = {
  MuiButton: {
    styleOverrides: {
      root: {
        borderRadius: 12,
        padding: '12px 24px',
        fontSize: '0.875rem',
        fontWeight: 600,
        textTransform: 'none' as const,
        boxShadow: 'none',
        '&:hover': {
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        },
      },
      contained: {
        '&:hover': {
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        },
      },
    },
  },
  MuiCard: {
    styleOverrides: {
      root: {
        borderRadius: 16,
        boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
        border: '1px solid #F0F0F0',
        '&:hover': {
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.12)',
        },
      },
    },
  },
  MuiTextField: {
    styleOverrides: {
      root: {
        '& .MuiOutlinedInput-root': {
          borderRadius: 12,
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: colors.primary.main,
          },
        },
      },
    },
  },
  MuiChip: {
    styleOverrides: {
      root: {
        borderRadius: 8,
        fontWeight: 500,
      },
    },
  },
  MuiAppBar: {
    styleOverrides: {
      root: {
        boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
        backgroundColor: '#FFFFFF',
        color: colors.text.primary,
      },
    },
  },
  MuiDrawer: {
    styleOverrides: {
      paper: {
        borderRadius: '0 16px 16px 0',
        border: 'none',
        boxShadow: '4px 0 20px rgba(0, 0, 0, 0.08)',
      },
    },
  },
  MuiDialog: {
    styleOverrides: {
      paper: {
        borderRadius: 16,
        padding: '8px',
      },
    },
  },
  MuiAlert: {
    styleOverrides: {
      root: {
        borderRadius: 12,
        '& .MuiAlert-icon': {
          fontSize: '1.25rem',
        },
      },
    },
  },
};

// Breakpoints for responsive design
const breakpoints = {
  values: {
    xs: 0,
    sm: 600,
    md: 900,
    lg: 1200,
    xl: 1536,
  },
};

// Create the theme
export const theme = createTheme({
  palette: colors,
  typography,
  components,
  breakpoints,
  spacing: 8,
  shape: {
    borderRadius: 12,
  },
});

// Export health colors for use in components
export { healthColors };

// Utility functions for health status colors
export const getBloodPressureColor = (systolic: number, diastolic: number) => {
  if (systolic >= 140 || diastolic >= 90) return healthColors.bloodPressure.high;
  if (systolic >= 130 || diastolic >= 80) return healthColors.bloodPressure.elevated;
  return healthColors.bloodPressure.normal;
};

export const getBloodSugarColor = (value: number, type: 'fasting' | 'postMeal' | 'random') => {
  if (type === 'fasting') {
    if (value >= 126) return healthColors.bloodSugar.diabetic;
    if (value >= 100) return healthColors.bloodSugar.prediabetic;
    return healthColors.bloodSugar.normal;
  }
  if (type === 'postMeal') {
    if (value >= 200) return healthColors.bloodSugar.diabetic;
    if (value >= 140) return healthColors.bloodSugar.prediabetic;
    return healthColors.bloodSugar.normal;
  }
  // random
  if (value >= 200) return healthColors.bloodSugar.diabetic;
  return healthColors.bloodSugar.normal;
};

export const getBMIColor = (bmi: number) => {
  if (bmi >= 30) return healthColors.bmi.obese;
  if (bmi >= 25) return healthColors.bmi.overweight;
  if (bmi >= 18.5) return healthColors.bmi.normal;
  return healthColors.bmi.underweight;
};

export const getRiskLevelColor = (level: 'low' | 'medium' | 'high' | 'critical') => {
  return healthColors.riskLevel[level];
};
