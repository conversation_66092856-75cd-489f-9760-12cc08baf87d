import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Button,
  Alert,
  LinearProgress,
  IconButton,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Medication as MedicationIcon,
  QrCode as QrCodeIcon,
  Emergency as EmergencyIcon,
  Favorite as HeartIcon,
  MonitorHeart as MonitorIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { DashboardMetrics, HealthAlert } from '../types';
import HealthScoreCard from '../components/dashboard/HealthScoreCard';
import VitalsChart from '../components/dashboard/VitalsChart';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockMetrics: DashboardMetrics = {
      totalVisits: 12,
      lastVisitDate: new Date('2024-01-15'),
      activeConditions: 2,
      upcomingAppointments: 1,
      medicationReminders: 3,
      healthScore: 78,
      recentAlerts: [
        {
          id: '1',
          type: 'medication',
          severity: 'warning',
          title: 'Medication Reminder',
          message: 'Time to take your blood pressure medication',
          actionRequired: true,
          createdAt: new Date(),
        },
        {
          id: '2',
          type: 'vitals',
          severity: 'info',
          title: 'Health Check',
          message: 'Record your daily vitals for better tracking',
          actionRequired: false,
          createdAt: new Date(),
        },
      ],
    };

    setTimeout(() => {
      setMetrics(mockMetrics);
      setLoading(false);
    }, 1000);
  }, []);

  // Mock health score factors
  const healthFactors = [
    { name: 'Blood Pressure', status: 'good' as const, impact: 5 },
    { name: 'Exercise', status: 'warning' as const, impact: -2 },
    { name: 'Medication Adherence', status: 'good' as const, impact: 8 },
    { name: 'Sleep Quality', status: 'warning' as const, impact: -3 },
    { name: 'Diet', status: 'good' as const, impact: 6 },
  ];

  // Mock vitals data for charts
  const vitalsData = [
    {
      date: '2024-01-01',
      bloodPressureSystolic: 118,
      bloodPressureDiastolic: 78,
      bloodSugar: 92,
      weight: 70.2,
      heartRate: 72,
    },
    {
      date: '2024-01-02',
      bloodPressureSystolic: 122,
      bloodPressureDiastolic: 82,
      bloodSugar: 88,
      weight: 70.1,
      heartRate: 75,
    },
    {
      date: '2024-01-03',
      bloodPressureSystolic: 120,
      bloodPressureDiastolic: 80,
      bloodSugar: 95,
      weight: 70.0,
      heartRate: 73,
    },
    {
      date: '2024-01-04',
      bloodPressureSystolic: 115,
      bloodPressureDiastolic: 75,
      bloodSugar: 90,
      weight: 69.8,
      heartRate: 71,
    },
    {
      date: '2024-01-05',
      bloodPressureSystolic: 119,
      bloodPressureDiastolic: 79,
      bloodSugar: 93,
      weight: 69.9,
      heartRate: 74,
    },
  ];

  const quickActions = [
    {
      title: 'Record Vitals',
      description: 'Add today\'s health measurements',
      icon: <MonitorIcon />,
      color: 'primary',
      action: () => navigate('/health-timeline'),
    },
    {
      title: 'Emergency Access',
      description: 'Quick QR code for emergencies',
      icon: <QrCodeIcon />,
      color: 'error',
      action: () => navigate('/emergency-access'),
    },
    {
      title: 'View Reports',
      description: 'Check hospital visit history',
      icon: <ScheduleIcon />,
      color: 'info',
      action: () => navigate('/hospital-reports'),
    },
    {
      title: 'Medications',
      description: 'Manage your prescriptions',
      icon: <MedicationIcon />,
      color: 'success',
      action: () => navigate('/health-timeline'),
    },
  ];

  const healthMetrics = [
    {
      title: 'Blood Pressure',
      value: '120/80',
      unit: 'mmHg',
      status: 'normal',
      trend: 'stable',
      lastUpdated: '2 hours ago',
    },
    {
      title: 'Blood Sugar',
      value: '95',
      unit: 'mg/dL',
      status: 'normal',
      trend: 'down',
      lastUpdated: '1 day ago',
    },
    {
      title: 'Weight',
      value: '70.5',
      unit: 'kg',
      status: 'normal',
      trend: 'up',
      lastUpdated: '3 days ago',
    },
    {
      title: 'Heart Rate',
      value: '72',
      unit: 'bpm',
      status: 'normal',
      trend: 'stable',
      lastUpdated: '2 hours ago',
    },
  ];

  if (loading) {
    return (
      <Box sx={{ width: '100%', mt: 4 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2, textAlign: 'center' }}>
          Loading your health dashboard...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" fontWeight={700} gutterBottom>
            Welcome back, John! 👋
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's your health summary for today
          </Typography>
        </Box>
      </motion.div>

      <Grid container spacing={3}>
        {/* Health Score Card */}
        <Grid item xs={12} md={4}>
          <HealthScoreCard
            score={metrics?.healthScore || 0}
            trend="up"
            factors={healthFactors}
          />
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            {[
              { label: 'Total Visits', value: metrics?.totalVisits, icon: <ScheduleIcon /> },
              { label: 'Active Conditions', value: metrics?.activeConditions, icon: <WarningIcon /> },
              { label: 'Upcoming Appointments', value: metrics?.upcomingAppointments, icon: <ScheduleIcon /> },
              { label: 'Medication Reminders', value: metrics?.medicationReminders, icon: <MedicationIcon /> },
            ].map((stat, index) => (
              <Grid item xs={6} key={stat.label}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                >
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 3 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'primary.main',
                          width: 48,
                          height: 48,
                          mx: 'auto',
                          mb: 2,
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight={700}>
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.label}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* Health Trends Chart */}
        <Grid item xs={12}>
          <VitalsChart data={vitalsData} />
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Quick Actions
                </Typography>
                <Grid container spacing={2}>
                  {quickActions.map((action, index) => (
                    <Grid item xs={12} sm={6} key={action.title}>
                      <Button
                        fullWidth
                        variant="outlined"
                        onClick={action.action}
                        sx={{
                          p: 2,
                          height: 'auto',
                          flexDirection: 'column',
                          alignItems: 'flex-start',
                          textAlign: 'left',
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Avatar
                            sx={{
                              bgcolor: `${action.color}.main`,
                              width: 32,
                              height: 32,
                              mr: 2,
                            }}
                          >
                            {action.icon}
                          </Avatar>
                          <Typography variant="subtitle1" fontWeight={600}>
                            {action.title}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {action.description}
                        </Typography>
                      </Button>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Recent Alerts */}
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Health Alerts
                </Typography>
                {metrics?.recentAlerts.map((alert, index) => (
                  <Box key={alert.id}>
                    <Alert
                      severity={alert.severity}
                      sx={{ mb: 2 }}
                      action={
                        alert.actionRequired && (
                          <Button size="small" color="inherit">
                            Action
                          </Button>
                        )
                      }
                    >
                      <Typography variant="subtitle2" fontWeight={600}>
                        {alert.title}
                      </Typography>
                      <Typography variant="body2">
                        {alert.message}
                      </Typography>
                    </Alert>
                    {index < metrics.recentAlerts.length - 1 && <Divider sx={{ my: 1 }} />}
                  </Box>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
