import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  IconButton,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Tabs,
  Tab,
} from '@mui/material';
import {
  QrCode as QrCodeIcon,
  Security as SecurityIcon,
  Fingerprint as FingerprintIcon,
  Pin as PinIcon,
  Sms as SmsIcon,
  Emergency as EmergencyIcon,
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  LocalHospital as HospitalIcon,
  Phone as PhoneIcon,
  ContentCopy as CopyIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import QRCode from 'qrcode.react';
import { format } from 'date-fns';
import { EmergencyAccess as EmergencyAccessType } from '../types';

const EmergencyAccess: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [qrCode, setQrCode] = useState('');
  const [otpDialogOpen, setOtpDialogOpen] = useState(false);
  const [pinDialogOpen, setPinDialogOpen] = useState(false);
  const [biometricDialogOpen, setBiometricDialogOpen] = useState(false);
  const [accessGranted, setAccessGranted] = useState(false);
  const [accessHistory, setAccessHistory] = useState<EmergencyAccessType[]>([]);
  const [otp, setOtp] = useState('');
  const [pin, setPin] = useState('');
  const [biometricStep, setBiometricStep] = useState(0);
  const [loading, setLoading] = useState(false);

  // Generate QR code data
  useEffect(() => {
    const emergencyData = {
      patientId: 'SWP123456',
      name: 'John Doe',
      bloodGroup: 'O+',
      emergencyContact: '+91-**********',
      allergies: ['Penicillin'],
      conditions: ['Hypertension', 'Type 2 Diabetes'],
      medications: ['Lisinopril 10mg', 'Metformin 500mg'],
      lastUpdated: new Date().toISOString(),
    };
    setQrCode(JSON.stringify(emergencyData));

    // Mock access history
    const mockHistory: EmergencyAccessType[] = [
      {
        id: '1',
        userId: 'user123',
        accessType: 'qr',
        accessedBy: {
          name: 'Dr. Sarah Wilson',
          designation: 'Emergency Physician',
          hospitalId: 'AIIMS001',
          phone: '+91-**********',
        },
        accessedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        dataAccessed: ['Basic Info', 'Medical Conditions', 'Current Medications'],
        reason: 'Emergency room visit - chest pain',
        location: {
          latitude: 28.5665,
          longitude: 77.2010,
          address: 'AIIMS Emergency Department, New Delhi',
        },
      },
      {
        id: '2',
        userId: 'user123',
        accessType: 'otp',
        accessedBy: {
          name: 'Dr. Rajesh Kumar',
          designation: 'Cardiologist',
          phone: '+91-**********',
        },
        accessedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        dataAccessed: ['Vitals History', 'Lab Reports', 'Prescription History'],
        reason: 'Routine cardiology consultation',
      },
    ];
    setAccessHistory(mockHistory);
  }, []);

  const handleOtpVerification = async () => {
    setLoading(true);
    // Simulate OTP verification
    await new Promise(resolve => setTimeout(resolve, 2000));

    if (otp === '123456') {
      setAccessGranted(true);
      setOtpDialogOpen(false);
    } else {
      alert('Invalid OTP. Please try again.');
    }
    setLoading(false);
  };

  const handlePinVerification = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1500));

    if (pin === '1234') {
      setAccessGranted(true);
      setPinDialogOpen(false);
    } else {
      alert('Invalid PIN. Please try again.');
    }
    setLoading(false);
  };

  const handleBiometricAuth = async () => {
    setLoading(true);
    setBiometricStep(1);

    // Simulate biometric scanning
    await new Promise(resolve => setTimeout(resolve, 3000));
    setBiometricStep(2);

    await new Promise(resolve => setTimeout(resolve, 1000));
    setAccessGranted(true);
    setBiometricDialogOpen(false);
    setLoading(false);
    setBiometricStep(0);
  };

  const copyQrData = () => {
    navigator.clipboard.writeText(qrCode);
    alert('Emergency data copied to clipboard!');
  };

  const refreshQrCode = () => {
    // In real app, this would generate a new QR code with updated timestamp
    const emergencyData = JSON.parse(qrCode);
    emergencyData.lastUpdated = new Date().toISOString();
    setQrCode(JSON.stringify(emergencyData));
  };

  const emergencyInfo = {
    patientId: 'SWP123456',
    name: 'John Doe',
    age: 45,
    bloodGroup: 'O+',
    emergencyContact: {
      name: 'Jane Doe',
      relation: 'Spouse',
      phone: '+91-**********',
    },
    allergies: ['Penicillin', 'Shellfish'],
    conditions: ['Hypertension', 'Type 2 Diabetes'],
    medications: [
      'Lisinopril 10mg - Once daily',
      'Metformin 500mg - Twice daily',
    ],
    lastVitals: {
      bloodPressure: '120/80 mmHg',
      bloodSugar: '95 mg/dL',
      weight: '70.5 kg',
      date: '2024-01-15',
    },
  };

  const accessMethods = [
    {
      title: 'QR Code Access',
      description: 'Instant access for emergency responders',
      icon: <QrCodeIcon />,
      color: 'primary',
      action: () => setSelectedTab(0),
    },
    {
      title: 'OTP Verification',
      description: 'Secure access with one-time password',
      icon: <SmsIcon />,
      color: 'secondary',
      action: () => setOtpDialogOpen(true),
    },
    {
      title: 'PIN Access',
      description: 'Quick access with personal PIN',
      icon: <PinIcon />,
      color: 'success',
      action: () => setPinDialogOpen(true),
    },
    {
      title: 'Biometric Auth',
      description: 'Fingerprint or face recognition',
      icon: <FingerprintIcon />,
      color: 'warning',
      action: () => setBiometricDialogOpen(true),
    },
  ];

  const tabContent = [
    {
      label: 'QR Code',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Emergency QR Code
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Show this QR code to medical professionals for instant access to your critical health information
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                  <Paper elevation={3} sx={{ p: 2, borderRadius: 2 }}>
                    <QRCode
                      value={qrCode}
                      size={200}
                      level="H"
                      includeMargin
                    />
                  </Paper>
                </Box>

                <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                  <Button
                    variant="outlined"
                    startIcon={<CopyIcon />}
                    onClick={copyQrData}
                    size="small"
                  >
                    Copy Data
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={refreshQrCode}
                    size="small"
                  >
                    Refresh
                  </Button>
                </Box>

                <Alert severity="info" sx={{ mt: 3, textAlign: 'left' }}>
                  <Typography variant="body2">
                    <strong>For Medical Professionals:</strong> Scan this QR code to access patient's emergency medical information including allergies, current medications, and emergency contacts.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Emergency Information
                </Typography>

                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Patient ID"
                      secondary={emergencyInfo.patientId}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Name & Age"
                      secondary={`${emergencyInfo.name}, ${emergencyInfo.age} years`}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <SecurityIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Blood Group"
                      secondary={emergencyInfo.bloodGroup}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Emergency Contact"
                      secondary={`${emergencyInfo.emergencyContact.name} (${emergencyInfo.emergencyContact.relation}) - ${emergencyInfo.emergencyContact.phone}`}
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                  Allergies
                </Typography>
                <Box sx={{ mb: 2 }}>
                  {emergencyInfo.allergies.map((allergy, index) => (
                    <Chip
                      key={index}
                      label={allergy}
                      color="error"
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>

                <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                  Medical Conditions
                </Typography>
                <Box sx={{ mb: 2 }}>
                  {emergencyInfo.conditions.map((condition, index) => (
                    <Chip
                      key={index}
                      label={condition}
                      color="warning"
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>

                <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                  Current Medications
                </Typography>
                <List dense>
                  {emergencyInfo.medications.map((medication, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemText
                        primary={medication}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ),
    },
    {
      label: 'Access History',
      content: (
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight={600} gutterBottom>
              Recent Emergency Access Log
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Track who has accessed your emergency medical information
            </Typography>

            <List>
              {accessHistory.map((access, index) => (
                <React.Fragment key={access.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemIcon>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <HospitalIcon />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1" fontWeight={600}>
                            {access.accessedBy.name}
                          </Typography>
                          <Chip
                            label={access.accessType.toUpperCase()}
                            size="small"
                            color="primary"
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            {access.accessedBy.designation}
                            {access.accessedBy.hospitalId && ` • ${access.accessedBy.hospitalId}`}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            📞 {access.accessedBy.phone}
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            <strong>Reason:</strong> {access.reason}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Data Accessed:</strong> {access.dataAccessed.join(', ')}
                          </Typography>
                          {access.location && (
                            <Typography variant="body2">
                              <strong>Location:</strong> {access.location.address}
                            </Typography>
                          )}
                          <Typography variant="caption" color="text.secondary">
                            {format(access.accessedAt, 'MMMM dd, yyyy • hh:mm a')}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < accessHistory.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      ),
    },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" fontWeight={700} gutterBottom>
            Emergency Access Vault
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Secure emergency access to your critical health information for medical professionals
          </Typography>
        </Box>
      </motion.div>

      {/* Access Methods Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {accessMethods.map((method, index) => (
          <Grid item xs={12} sm={6} md={3} key={method.title}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  },
                }}
                onClick={method.action}
              >
                <CardContent sx={{ textAlign: 'center', py: 3 }}>
                  <Avatar
                    sx={{
                      bgcolor: `${method.color}.main`,
                      width: 56,
                      height: 56,
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    {method.icon}
                  </Avatar>
                  <Typography variant="h6" fontWeight={600} gutterBottom>
                    {method.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {method.description}
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={selectedTab}
          onChange={(_, newValue) => setSelectedTab(newValue)}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {tabContent.map((tab, index) => (
            <Tab key={index} label={tab.label} />
          ))}
        </Tabs>
      </Card>

      <motion.div
        key={selectedTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mt: 3 }}>
          {tabContent[selectedTab].content}
        </Box>
      </motion.div>

      {/* OTP Dialog */}
      <Dialog open={otpDialogOpen} onClose={() => setOtpDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>OTP Verification</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Enter the OTP sent to your registered mobile number ending with ****3210
          </Alert>
          <TextField
            autoFocus
            margin="dense"
            label="Enter OTP"
            type="text"
            fullWidth
            variant="outlined"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            placeholder="123456"
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Demo OTP: 123456
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOtpDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleOtpVerification}
            variant="contained"
            disabled={loading || otp.length !== 6}
          >
            {loading ? <CircularProgress size={20} /> : 'Verify'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* PIN Dialog */}
      <Dialog open={pinDialogOpen} onClose={() => setPinDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>PIN Authentication</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Enter your 4-digit emergency access PIN
          </Alert>
          <TextField
            autoFocus
            margin="dense"
            label="Enter PIN"
            type="password"
            fullWidth
            variant="outlined"
            value={pin}
            onChange={(e) => setPin(e.target.value)}
            placeholder="****"
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Demo PIN: 1234
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPinDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handlePinVerification}
            variant="contained"
            disabled={loading || pin.length !== 4}
          >
            {loading ? <CircularProgress size={20} /> : 'Authenticate'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Biometric Dialog */}
      <Dialog open={biometricDialogOpen} onClose={() => setBiometricDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Biometric Authentication</DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Stepper activeStep={biometricStep} alternativeLabel>
              <Step>
                <StepLabel>Place Finger</StepLabel>
              </Step>
              <Step>
                <StepLabel>Scanning</StepLabel>
              </Step>
              <Step>
                <StepLabel>Verified</StepLabel>
              </Step>
            </Stepper>

            <Box sx={{ mt: 4, mb: 2 }}>
              <AnimatePresence mode="wait">
                {biometricStep === 0 && (
                  <motion.div
                    key="step0"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <FingerprintIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h6">Place your finger on the sensor</Typography>
                  </motion.div>
                )}
                {biometricStep === 1 && (
                  <motion.div
                    key="step1"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                      <CircularProgress size={80} />
                      <Box
                        sx={{
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          position: 'absolute',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <FingerprintIcon sx={{ fontSize: 40 }} />
                      </Box>
                    </Box>
                    <Typography variant="h6" sx={{ mt: 2 }}>Scanning fingerprint...</Typography>
                  </motion.div>
                )}
                {biometricStep === 2 && (
                  <motion.div
                    key="step2"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <CheckIcon sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
                    <Typography variant="h6">Authentication successful!</Typography>
                  </motion.div>
                )}
              </AnimatePresence>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBiometricDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleBiometricAuth}
            variant="contained"
            disabled={loading || biometricStep > 0}
          >
            Start Scan
          </Button>
        </DialogActions>
      </Dialog>

      {/* Access Granted Dialog */}
      <Dialog open={accessGranted} onClose={() => setAccessGranted(false)} maxWidth="sm" fullWidth>
        <DialogContent sx={{ textAlign: 'center', py: 4 }}>
          <CheckIcon sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Access Granted!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Emergency medical information is now accessible to authorized medical personnel.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAccessGranted(false)} variant="contained" fullWidth>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EmergencyAccess;
