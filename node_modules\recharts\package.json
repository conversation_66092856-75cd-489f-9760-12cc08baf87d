{"name": "recharts", "version": "3.0.2", "description": "React charts", "main": "lib/index.js", "module": "es6/index.js", "jsnext:main": "es6/index.js", "types": "types/index.d.ts", "sideEffects": false, "files": ["*.md", "es6", "lib", "umd", "types"], "keywords": ["react", "reactjs", "chart", "react-component"], "scripts": {"prepare": "husky", "build": "npm run build-types && npm run build-cjs && npm run build-es6 && npm run build-umd && npm run test-build-output", "build-cjs": "rimraf lib && cross-env NODE_ENV=commonjs babel ./src -d lib --extensions '.js,.ts,.tsx'", "build-es6": "rimraf es6 && cross-env NODE_ENV=es6 babel ./src -d es6 --extensions '.js,.ts,.tsx'", "build-umd": "rimraf umd && cross-env NODE_ENV=production webpack -o umd", "build-types": "rimraf types && npm run tsc", "check-types": "tsc --noEmit && npm run check-types-test && npm run check-types-storybook", "check-types-test": "tsc --project test/tsconfig.json", "check-types-storybook": "tsc --project storybook/tsconfig.json", "test": "vitest run --config vitest.config.mts", "test-coverage": "vitest run --config vitest.config.mts --coverage", "test-mutation": "stryker run", "test-watch": "vitest --config vitest.config.mts", "lint": "eslint \"./src/**/*.{ts,tsx}\"", "lint-test": "eslint \"./test/**/*.{ts,tsx}\"", "lint-storybook": "eslint \"./storybook/**/*.{ts,tsx}\"", "autofix": "eslint \"./src/**/*.{ts,tsx}\" --fix", "analyse": "cross-env NODE_ENV=analyse webpack -o umd/Recharts.js", "tsc": "tsc", "storybook": "npx storybook dev -p 6006 -c storybook", "build-storybook": "npx storybook build -o storybook/public -c storybook", "chromatic": "npx chromatic", "test-build-output": "vitest run --config scripts/vitest-build.config.ts", "test-storybook": "test-storybook --config-dir storybook", "test-storybook:url": "test-storybook --url http://127.0.0.1:9009 --config-dir storybook", "test-storybook:ci": "concurrently -k -s first -n \"SB,TEST\" -c \"magenta,blue\" \"npm run build-storybook --quiet && npx http-server storybook/public --port 9009 --silent\" \"wait-on tcp:127.0.0.1:9009 && npm run test-storybook:url --maxWorkers=2\""}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/recharts/recharts.git"}, "author": {"name": "recharts group"}, "bugs": {"url": "https://github.com/recharts/recharts/issues"}, "homepage": "https://github.com/recharts/recharts", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-is": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "dependencies": {"@reduxjs/toolkit": "1.x.x || 2.x.x", "clsx": "^2.1.1", "decimal.js-light": "^2.5.1", "es-toolkit": "^1.39.3", "eventemitter3": "^5.0.1", "immer": "^10.1.1", "react-redux": "8.x.x || 9.x.x", "reselect": "5.1.1", "tiny-invariant": "^1.3.3", "use-sync-external-store": "^1.2.2", "victory-vendor": "^37.0.2"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/core": "^7.24.9", "@babel/preset-env": "^7.24.8", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/runtime": "^7.26.10", "@chromatic-com/storybook": "^3.2.6", "@codecov/bundle-analyzer": "^1.9.1", "@reduxjs/toolkit": "^1.9.7", "@storybook/addon-a11y": "^8.6.13", "@storybook/addon-docs": "^8.6.13", "@storybook/addon-essentials": "^8.6.13", "@storybook/addon-interactions": "^8.6.13", "@storybook/addon-links": "^8.6.13", "@storybook/addon-mdx-gfm": "^8.6.13", "@storybook/addon-webpack5-compiler-swc": "^3.0.0", "@storybook/components": "^8.6.13", "@storybook/icons": "^1.2.12", "@storybook/manager-api": "^8.6.13", "@storybook/preview-api": "^8.6.13", "@storybook/react": "^8.6.13", "@storybook/react-webpack5": "^8.6.13", "@storybook/source-loader": "^8.6.13", "@storybook/test": "^8.6.13", "@storybook/test-runner": "^0.22.0", "@storybook/theming": "^8.6.13", "@storybook/types": "^8.6.13", "@stryker-mutator/typescript-checker": "^8.2.6", "@stryker-mutator/vitest-runner": "^8.2.6", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/d3-interpolate": "^3.0.4", "@types/d3-shape": "^3.1.6", "@types/d3-time-format": "^4.0.3", "@types/node": "20.x.x", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-is": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.0.1", "@typescript-eslint/parser": "^8.0.1", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^3.2.3", "babel-loader": "^9.1.3", "babel-plugin-dev-expression": "^0.2.3", "browserslist": "^4.23.2", "chromatic": "^11.5.6", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "d3-scale-chromatic": "^3.1.0", "d3-time": "^3.1.0", "d3-time-format": "^4.1.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.12.0", "glob": "^11.0.0", "husky": "^9.1.1", "jsdom": "^24.1.1", "lint-staged": "^15.2.7", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-is": "^18.3.1", "react-redux": "^8.1.3", "react-router-dom": "^6.25.1", "rimraf": "^6.0.1", "storybook": "^8.6.13", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "update-browserslist-db": "^1.1.0", "vitest": "^3.2.3", "vitest-axe": "^0.1.0", "webpack": "^5.93.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}, "engines": {"node": ">=18"}, "license": "MIT"}