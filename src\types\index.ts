// User and Authentication Types
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  dateOfBirth: Date;
  gender: 'male' | 'female' | 'other';
  bloodGroup: string;
  emergencyContact: {
    name: string;
    phone: string;
    relation: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  profilePicture?: string;
  qrCode: string;
  createdAt: Date;
  updatedAt: Date;
}

// Health Data Types
export interface HealthVitals {
  id: string;
  userId: string;
  date: Date;
  bloodPressure: {
    systolic: number;
    diastolic: number;
  };
  bloodSugar: {
    fasting?: number;
    postMeal?: number;
    random?: number;
  };
  weight: number;
  height: number;
  bmi: number;
  heartRate?: number;
  temperature?: number;
  oxygenSaturation?: number;
  notes?: string;
}

export interface MedicalCondition {
  id: string;
  name: string;
  diagnosedDate: Date;
  severity: 'mild' | 'moderate' | 'severe';
  status: 'active' | 'resolved' | 'chronic';
  medications: string[];
  notes?: string;
}

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  startDate: Date;
  endDate?: Date;
  prescribedBy: string;
  purpose: string;
  sideEffects?: string[];
}

// Hospital and Medical Records
export interface HospitalVisit {
  id: string;
  userId: string;
  hospitalName: string;
  hospitalId: string;
  doctorName: string;
  doctorSpecialty: string;
  visitDate: Date;
  visitType: 'consultation' | 'emergency' | 'surgery' | 'checkup' | 'follow-up';
  diagnosis: string[];
  symptoms: string[];
  treatment: string;
  prescriptions: Medication[];
  labReports: LabReport[];
  bills: MedicalBill[];
  notes?: string;
  followUpDate?: Date;
}

export interface LabReport {
  id: string;
  testName: string;
  testDate: Date;
  results: {
    parameter: string;
    value: string;
    unit: string;
    normalRange: string;
    status: 'normal' | 'high' | 'low' | 'critical';
  }[];
  reportUrl?: string;
  labName: string;
}

export interface MedicalBill {
  id: string;
  billNumber: string;
  date: Date;
  hospitalName: string;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  items: {
    description: string;
    quantity: number;
    rate: number;
    amount: number;
  }[];
  paymentStatus: 'paid' | 'pending' | 'partial';
  insuranceClaimed?: boolean;
  billUrl?: string;
}

// Vaccination Types
export interface Vaccination {
  id: string;
  vaccineName: string;
  vaccineType: string;
  administeredDate: Date;
  nextDueDate?: Date;
  batchNumber?: string;
  manufacturer?: string;
  administeredBy: string;
  location: string;
  sideEffects?: string[];
  certificateUrl?: string;
}

// Women's Health Types
export interface MenstrualCycle {
  id: string;
  userId: string;
  startDate: Date;
  endDate?: Date;
  cycleLength: number;
  flowIntensity: 'light' | 'medium' | 'heavy';
  symptoms: string[];
  mood: string[];
  notes?: string;
}

export interface PregnancyRecord {
  id: string;
  userId: string;
  conceptionDate: Date;
  expectedDueDate: Date;
  currentWeek: number;
  checkups: {
    date: Date;
    weight: number;
    bloodPressure: { systolic: number; diastolic: number };
    notes: string;
    doctorName: string;
  }[];
  complications?: string[];
  medications: string[];
  status: 'ongoing' | 'completed' | 'terminated';
}

// AI Prediction Types
export interface HealthPrediction {
  id: string;
  userId: string;
  predictionType: 'diabetes' | 'hypertension' | 'heart_disease' | 'general_health';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  factors: string[];
  recommendations: string[];
  generatedAt: Date;
  basedOnData: {
    vitals: HealthVitals[];
    conditions: MedicalCondition[];
    timeRange: { start: Date; end: Date };
  };
}

// Emergency Access Types
export interface EmergencyAccess {
  id: string;
  userId: string;
  accessType: 'qr' | 'otp' | 'biometric' | 'pin';
  accessedBy: {
    name: string;
    designation: string;
    hospitalId?: string;
    phone: string;
  };
  accessedAt: Date;
  dataAccessed: string[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  reason: string;
}

// Voice Input Types
export interface VoiceInput {
  id: string;
  userId: string;
  audioUrl: string;
  transcription: string;
  language: 'en' | 'hi' | 'te';
  confidence: number;
  processedAt: Date;
  action: string;
  success: boolean;
}

// Dashboard Types
export interface DashboardMetrics {
  totalVisits: number;
  lastVisitDate?: Date;
  activeConditions: number;
  upcomingAppointments: number;
  medicationReminders: number;
  healthScore: number;
  recentAlerts: HealthAlert[];
}

export interface HealthAlert {
  id: string;
  type: 'medication' | 'appointment' | 'vitals' | 'emergency' | 'vaccination';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  message: string;
  actionRequired: boolean;
  createdAt: Date;
  readAt?: Date;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form Types
export interface VitalsFormData {
  bloodPressure: {
    systolic: string;
    diastolic: string;
  };
  bloodSugar: {
    fasting?: string;
    postMeal?: string;
    random?: string;
  };
  weight: string;
  height: string;
  heartRate?: string;
  temperature?: string;
  oxygenSaturation?: string;
  notes?: string;
}

// Chart Data Types
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface HealthTrendData {
  bloodPressure: {
    systolic: ChartDataPoint[];
    diastolic: ChartDataPoint[];
  };
  bloodSugar: ChartDataPoint[];
  weight: ChartDataPoint[];
  bmi: ChartDataPoint[];
}
