import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { theme } from './utils/theme';

// Import pages
import Dashboard from './pages/Dashboard';
import HealthTimeline from './pages/HealthTimeline';
import EmergencyAccess from './pages/EmergencyAccess';
import HospitalReports from './pages/HospitalReports';
import VaccinationTracker from './pages/VaccinationTracker';
import WomensHealth from './pages/WomensHealth';

// Import layout components
import Layout from './components/common/Layout';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/health-timeline" element={<HealthTimeline />} />
            <Route path="/emergency-access" element={<EmergencyAccess />} />
            <Route path="/hospital-reports" element={<HospitalReports />} />
            <Route path="/vaccination" element={<VaccinationTracker />} />
            <Route path="/womens-health" element={<WomensHealth />} />
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
