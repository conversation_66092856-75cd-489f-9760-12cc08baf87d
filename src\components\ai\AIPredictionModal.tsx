import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Card,
  CardContent,
} from '@mui/material';
import {
  Close as CloseIcon,
  Psychology as AIIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Lightbulb as LightbulbIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { HealthPrediction } from '../../types';

interface AIPredictionModalProps {
  open: boolean;
  onClose: () => void;
  userHealthData: any; // In real app, this would be properly typed health data
}

const AIPredictionModal: React.FC<AIPredictionModalProps> = ({
  open,
  onClose,
  userHealthData,
}) => {
  const [loading, setLoading] = useState(false);
  const [predictions, setPredictions] = useState<HealthPrediction[]>([]);
  const [analysisComplete, setAnalysisComplete] = useState(false);

  // Mock AI prediction generation
  const generatePredictions = async () => {
    setLoading(true);
    setAnalysisComplete(false);

    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 3000));

    const mockPredictions: HealthPrediction[] = [
      {
        id: '1',
        userId: 'user123',
        predictionType: 'hypertension',
        riskLevel: 'medium',
        confidence: 0.78,
        factors: [
          'Blood pressure trending upward',
          'Family history of hypertension',
          'Irregular exercise pattern',
        ],
        recommendations: [
          'Reduce sodium intake to less than 2300mg daily',
          'Increase cardiovascular exercise to 150 minutes per week',
          'Monitor blood pressure daily',
          'Consider stress management techniques',
        ],
        generatedAt: new Date(),
        basedOnData: {
          vitals: [],
          conditions: [],
          timeRange: { start: new Date(), end: new Date() },
        },
      },
      {
        id: '2',
        userId: 'user123',
        predictionType: 'diabetes',
        riskLevel: 'low',
        confidence: 0.85,
        factors: [
          'Blood sugar levels within normal range',
          'Healthy BMI maintained',
          'Regular physical activity',
        ],
        recommendations: [
          'Continue current healthy lifestyle',
          'Monitor blood sugar monthly',
          'Maintain balanced diet with complex carbohydrates',
        ],
        generatedAt: new Date(),
        basedOnData: {
          vitals: [],
          conditions: [],
          timeRange: { start: new Date(), end: new Date() },
        },
      },
      {
        id: '3',
        userId: 'user123',
        predictionType: 'general_health',
        riskLevel: 'low',
        confidence: 0.82,
        factors: [
          'Consistent vital signs',
          'Good medication adherence',
          'Regular health checkups',
        ],
        recommendations: [
          'Continue current health management routine',
          'Schedule annual comprehensive health screening',
          'Consider adding vitamin D supplementation',
        ],
        generatedAt: new Date(),
        basedOnData: {
          vitals: [],
          conditions: [],
          timeRange: { start: new Date(), end: new Date() },
        },
      },
    ];

    setPredictions(mockPredictions);
    setLoading(false);
    setAnalysisComplete(true);
  };

  useEffect(() => {
    if (open) {
      generatePredictions();
    }
  }, [open]);

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'high': return '#F44336';
      case 'critical': return '#D32F2F';
      default: return '#9E9E9E';
    }
  };

  const getRiskIcon = (level: string) => {
    switch (level) {
      case 'low': return <CheckIcon sx={{ color: getRiskColor(level) }} />;
      case 'medium': return <WarningIcon sx={{ color: getRiskColor(level) }} />;
      case 'high': return <ErrorIcon sx={{ color: getRiskColor(level) }} />;
      case 'critical': return <ErrorIcon sx={{ color: getRiskColor(level) }} />;
      default: return <CheckIcon sx={{ color: getRiskColor(level) }} />;
    }
  };

  const getPredictionTitle = (type: string) => {
    switch (type) {
      case 'diabetes': return 'Diabetes Risk Assessment';
      case 'hypertension': return 'Hypertension Risk Assessment';
      case 'heart_disease': return 'Heart Disease Risk Assessment';
      case 'general_health': return 'General Health Assessment';
      default: return 'Health Assessment';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AIIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" fontWeight={600}>
            AI Health Predictions
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3 }}>
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <AIIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Analyzing Your Health Data
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Our AI is processing your health trends, vitals, and medical history...
              </Typography>
              <LinearProgress sx={{ mb: 2 }} />
              <Typography variant="caption" color="text.secondary">
                This may take a few moments
              </Typography>
            </Box>
          </motion.div>
        )}

        <AnimatePresence>
          {analysisComplete && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  <strong>AI Analysis Complete:</strong> Based on your health data from the past 3 months, 
                  here are personalized health insights and recommendations.
                </Typography>
              </Alert>

              {predictions.map((prediction, index) => (
                <motion.div
                  key={prediction.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card sx={{ mb: 3, border: `2px solid ${getRiskColor(prediction.riskLevel)}20` }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="h6" fontWeight={600}>
                          {getPredictionTitle(prediction.predictionType)}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getRiskIcon(prediction.riskLevel)}
                          <Chip
                            label={`${prediction.riskLevel.toUpperCase()} RISK`}
                            size="small"
                            sx={{
                              backgroundColor: getRiskColor(prediction.riskLevel),
                              color: 'white',
                              fontWeight: 600,
                            }}
                          />
                          <Chip
                            label={`${Math.round(prediction.confidence * 100)}% confidence`}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </Box>

                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Risk Factors Identified:
                      </Typography>
                      <List dense sx={{ mb: 2 }}>
                        {prediction.factors.map((factor, factorIndex) => (
                          <ListItem key={factorIndex} sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <TrendingUpIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={factor}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>

                      <Divider sx={{ my: 2 }} />

                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        <LightbulbIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                        AI Recommendations:
                      </Typography>
                      <List dense>
                        {prediction.recommendations.map((recommendation, recIndex) => (
                          <ListItem key={recIndex} sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <CheckIcon sx={{ fontSize: 16, color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={recommendation}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}

              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Disclaimer:</strong> These AI predictions are for informational purposes only 
                  and should not replace professional medical advice. Please consult with your healthcare 
                  provider for proper medical guidance.
                </Typography>
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose} color="inherit">
          Close
        </Button>
        {analysisComplete && (
          <Button variant="contained" onClick={() => {/* Share with doctor */}}>
            Share with Doctor
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AIPredictionModal;
