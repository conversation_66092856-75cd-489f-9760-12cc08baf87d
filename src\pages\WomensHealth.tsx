import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Tabs,
  Tab,

  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  FormControlLabel,
  Checkbox,
  Paper,
  LinearProgress,
  Fab,
} from '@mui/material';
import {
  Female as FemaleIcon,
  CalendarToday as CalendarIcon,
  Add as AddIcon,
  TrendingUp as TrendingUpIcon,
  Favorite as HeartIcon,
  Psychology as MoodIcon,
  LocalHospital as HealthIcon,
  Timeline as TimelineIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Baby as BabyIcon,
  MonitorHeart as VitalsIcon,
  Notifications as NotificationIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { format, addDays, subDays, differenceInDays, startOfMonth, endOfMonth, eachDayOfInterval } from 'date-fns';
import { MenstrualCycle, PregnancyRecord } from '../types';

const WomensHealth: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [periodDialogOpen, setPeriodDialogOpen] = useState(false);
  const [pregnancyDialogOpen, setPregnancyDialogOpen] = useState(false);
  const [menstrualCycles, setMenstrualCycles] = useState<MenstrualCycle[]>([]);
  const [pregnancyRecord, setPregnancyRecord] = useState<PregnancyRecord | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Mock data initialization
  useEffect(() => {
    const mockCycles: MenstrualCycle[] = [
      {
        id: '1',
        userId: 'user123',
        startDate: subDays(new Date(), 28),
        endDate: subDays(new Date(), 23),
        cycleLength: 28,
        flowIntensity: 'medium',
        symptoms: ['Cramps', 'Mood swings', 'Bloating'],
        mood: ['Irritable', 'Tired'],
        notes: 'Normal cycle, manageable symptoms',
      },
      {
        id: '2',
        userId: 'user123',
        startDate: subDays(new Date(), 56),
        endDate: subDays(new Date(), 51),
        cycleLength: 29,
        flowIntensity: 'heavy',
        symptoms: ['Severe cramps', 'Headache', 'Back pain'],
        mood: ['Anxious', 'Emotional'],
        notes: 'Heavier flow than usual',
      },
      {
        id: '3',
        userId: 'user123',
        startDate: subDays(new Date(), 85),
        endDate: subDays(new Date(), 80),
        cycleLength: 27,
        flowIntensity: 'light',
        symptoms: ['Mild cramps', 'Fatigue'],
        mood: ['Normal'],
        notes: 'Light cycle, minimal discomfort',
      },
    ];

    setMenstrualCycles(mockCycles);

    // Mock pregnancy record (if applicable)
    // setPregnancyRecord({
    //   id: '1',
    //   userId: 'user123',
    //   conceptionDate: subDays(new Date(), 120),
    //   expectedDueDate: addDays(new Date(), 160),
    //   currentWeek: 17,
    //   checkups: [],
    //   status: 'ongoing',
    //   medications: [],
    // });
  }, []);

  // Calculate cycle predictions
  const getNextPeriodPrediction = () => {
    if (menstrualCycles.length === 0) return null;

    const lastCycle = menstrualCycles[0];
    const averageCycleLength = menstrualCycles.reduce((sum, cycle) => sum + cycle.cycleLength, 0) / menstrualCycles.length;

    return addDays(lastCycle.startDate, Math.round(averageCycleLength));
  };

  const getOvulationPrediction = () => {
    const nextPeriod = getNextPeriodPrediction();
    if (!nextPeriod) return null;

    return subDays(nextPeriod, 14); // Ovulation typically occurs 14 days before next period
  };

  const getFertileWindow = () => {
    const ovulation = getOvulationPrediction();
    if (!ovulation) return null;

    return {
      start: subDays(ovulation, 5),
      end: addDays(ovulation, 1),
    };
  };

  const nextPeriod = getNextPeriodPrediction();
  const ovulation = getOvulationPrediction();
  const fertileWindow = getFertileWindow();

  const cycleInsights = {
    averageCycleLength: menstrualCycles.length > 0
      ? Math.round(menstrualCycles.reduce((sum, cycle) => sum + cycle.cycleLength, 0) / menstrualCycles.length)
      : 0,
    averageFlowDuration: menstrualCycles.length > 0
      ? Math.round(menstrualCycles.reduce((sum, cycle) =>
          cycle.endDate ? differenceInDays(cycle.endDate, cycle.startDate) + 1 : 5, 0) / menstrualCycles.length)
      : 0,
    commonSymptoms: ['Cramps', 'Mood swings', 'Bloating', 'Fatigue'],
    cycleRegularity: 'Regular', // Based on cycle length variation
  };

  // PCOS risk assessment
  const pcosRiskFactors = [
    { factor: 'Irregular periods', present: false, weight: 3 },
    { factor: 'Excessive hair growth', present: false, weight: 2 },
    { factor: 'Acne', present: true, weight: 1 },
    { factor: 'Weight gain', present: false, weight: 2 },
    { factor: 'Hair loss', present: false, weight: 2 },
    { factor: 'Insulin resistance', present: false, weight: 3 },
  ];

  const pcosRiskScore = pcosRiskFactors.reduce((score, factor) =>
    score + (factor.present ? factor.weight : 0), 0);
  const maxPcosScore = pcosRiskFactors.reduce((sum, factor) => sum + factor.weight, 0);
  const pcosRiskPercentage = (pcosRiskScore / maxPcosScore) * 100;

  const tabContent = [
    {
      label: 'Period Tracker',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Cycle Overview
                </Typography>

                {/* Cycle Predictions */}
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={12} sm={4}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'error.light', color: 'white' }}>
                      <Typography variant="subtitle2">Next Period</Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {nextPeriod ? format(nextPeriod, 'MMM dd') : 'N/A'}
                      </Typography>
                      <Typography variant="caption">
                        {nextPeriod ? `${differenceInDays(nextPeriod, new Date())} days` : ''}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'success.light', color: 'white' }}>
                      <Typography variant="subtitle2">Ovulation</Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {ovulation ? format(ovulation, 'MMM dd') : 'N/A'}
                      </Typography>
                      <Typography variant="caption">
                        {ovulation ? `${differenceInDays(ovulation, new Date())} days` : ''}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'info.light', color: 'white' }}>
                      <Typography variant="subtitle2">Fertile Window</Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {fertileWindow ? `${format(fertileWindow.start, 'MMM dd')} - ${format(fertileWindow.end, 'MMM dd')}` : 'N/A'}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                {/* Recent Cycles */}
                <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                  Recent Cycles
                </Typography>
                <List>
                  {menstrualCycles.slice(0, 3).map((cycle, index) => (
                    <React.Fragment key={cycle.id}>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                            <FemaleIcon sx={{ fontSize: 16 }} />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={format(cycle.startDate, 'MMMM dd, yyyy')}
                          secondary={
                            <Box>
                              <Typography variant="body2" component="span">
                                Duration: {cycle.endDate ? differenceInDays(cycle.endDate, cycle.startDate) + 1 : 'Ongoing'} days
                              </Typography>
                              <Typography variant="body2" component="span" sx={{ ml: 2 }}>
                                Flow: {cycle.flowIntensity}
                              </Typography>
                              <Typography variant="body2" component="span" sx={{ ml: 2 }}>
                                Cycle: {cycle.cycleLength} days
                              </Typography>
                              <Box sx={{ mt: 1 }}>
                                {cycle.symptoms.slice(0, 3).map((symptom, symptomIndex) => (
                                  <Chip
                                    key={symptomIndex}
                                    label={symptom}
                                    size="small"
                                    sx={{ mr: 0.5, mb: 0.5 }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < 2 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Cycle Insights
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText
                      primary="Average Cycle Length"
                      secondary={`${cycleInsights.averageCycleLength} days`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Average Flow Duration"
                      secondary={`${cycleInsights.averageFlowDuration} days`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Cycle Regularity"
                      secondary={cycleInsights.cycleRegularity}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Common Symptoms
                </Typography>
                <Box>
                  {cycleInsights.commonSymptoms.map((symptom, index) => (
                    <Chip
                      key={index}
                      label={symptom}
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ),
    },
    {
      label: 'PCOS Tracker',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  PCOS Risk Assessment
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Risk Score</Typography>
                    <Typography variant="body2">{Math.round(pcosRiskPercentage)}%</Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={pcosRiskPercentage}
                    color={pcosRiskPercentage > 60 ? 'error' : pcosRiskPercentage > 30 ? 'warning' : 'success'}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>

                <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                  Risk Factors
                </Typography>
                <List dense>
                  {pcosRiskFactors.map((factor, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {factor.present ? (
                          <CheckIcon color="error" />
                        ) : (
                          <CheckIcon color="disabled" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={factor.factor}
                        secondary={`Weight: ${factor.weight}`}
                      />
                    </ListItem>
                  ))}
                </List>

                <Alert
                  severity={pcosRiskPercentage > 60 ? 'error' : pcosRiskPercentage > 30 ? 'warning' : 'success'}
                  sx={{ mt: 2 }}
                >
                  <Typography variant="body2">
                    {pcosRiskPercentage > 60
                      ? 'High risk detected. Please consult with a gynecologist for proper evaluation.'
                      : pcosRiskPercentage > 30
                      ? 'Moderate risk. Monitor symptoms and consider consulting a healthcare provider.'
                      : 'Low risk. Continue maintaining healthy lifestyle habits.'
                    }
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Management Tips
                </Typography>

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <HeartIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Regular Exercise"
                      secondary="30 minutes of moderate exercise daily can help regulate hormones"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <HealthIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Balanced Diet"
                      secondary="Focus on low glycemic index foods and reduce processed sugars"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <MoodIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Stress Management"
                      secondary="Practice meditation, yoga, or other stress-reduction techniques"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <VitalsIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Regular Monitoring"
                      secondary="Track symptoms and maintain regular check-ups with healthcare providers"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ),
    },
    {
      label: 'Pregnancy Tracker',
      content: pregnancyRecord ? (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Pregnancy Progress - Week {pregnancyRecord.currentWeek}
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" gutterBottom>
                    Progress: {Math.round((pregnancyRecord.currentWeek / 40) * 100)}%
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={(pregnancyRecord.currentWeek / 40) * 100}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      Conception Date
                    </Typography>
                    <Typography variant="body2">
                      {format(pregnancyRecord.conceptionDate, 'MMMM dd, yyyy')}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      Expected Due Date
                    </Typography>
                    <Typography variant="body2">
                      {format(pregnancyRecord.expectedDueDate, 'MMMM dd, yyyy')}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  This Week
                </Typography>
                <Typography variant="body2">
                  Your baby is about the size of a {
                    pregnancyRecord.currentWeek < 8 ? 'blueberry' :
                    pregnancyRecord.currentWeek < 12 ? 'lime' :
                    pregnancyRecord.currentWeek < 16 ? 'avocado' :
                    pregnancyRecord.currentWeek < 20 ? 'banana' :
                    pregnancyRecord.currentWeek < 24 ? 'papaya' :
                    pregnancyRecord.currentWeek < 28 ? 'eggplant' :
                    pregnancyRecord.currentWeek < 32 ? 'coconut' :
                    pregnancyRecord.currentWeek < 36 ? 'pineapple' : 'watermelon'
                  }.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ) : (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <BabyIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No Active Pregnancy
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Start tracking your pregnancy journey when you're expecting
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setPregnancyDialogOpen(true)}
            >
              Start Pregnancy Tracking
            </Button>
          </CardContent>
        </Card>
      ),
    },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" fontWeight={700} gutterBottom>
              Women's Health
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Comprehensive tracking for your reproductive health journey
            </Typography>
          </Box>
        </Box>
      </motion.div>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 1 }}>
                <FemaleIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {cycleInsights.averageCycleLength}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg Cycle (days)
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 1 }}>
                <CalendarIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {nextPeriod ? differenceInDays(nextPeriod, new Date()) : 'N/A'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Days to Next Period
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 1 }}>
                <WarningIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {Math.round(pcosRiskPercentage)}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                PCOS Risk Score
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 1 }}>
                <TimelineIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {menstrualCycles.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Cycles Tracked
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={selectedTab}
          onChange={(_, newValue) => setSelectedTab(newValue)}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {tabContent.map((tab, index) => (
            <Tab key={index} label={tab.label} />
          ))}
        </Tabs>
      </Card>

      <motion.div
        key={selectedTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mt: 3 }}>
          {tabContent[selectedTab].content}
        </Box>
      </motion.div>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add period data"
        sx={{ position: 'fixed', bottom: 24, right: 24 }}
        onClick={() => {
          if (selectedTab === 0) setPeriodDialogOpen(true);
          else if (selectedTab === 2) setPregnancyDialogOpen(true);
        }}
      >
        <AddIcon />
      </Fab>

      {/* Add Period Dialog */}
      <Dialog
        open={periodDialogOpen}
        onClose={() => setPeriodDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Log Period</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                label="Start Date"
                type="date"
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="End Date"
                type="date"
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Flow Intensity</InputLabel>
                <Select label="Flow Intensity">
                  <MenuItem value="light">Light</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="heavy">Heavy</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Symptoms (select all that apply)
              </Typography>
              {['Cramps', 'Headache', 'Bloating', 'Mood swings', 'Fatigue', 'Back pain'].map((symptom) => (
                <FormControlLabel
                  key={symptom}
                  control={<Checkbox />}
                  label={symptom}
                />
              ))}
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Notes"
                fullWidth
                multiline
                rows={3}
                placeholder="Any additional notes about this cycle"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPeriodDialogOpen(false)}>Cancel</Button>
          <Button variant="contained">Save Period Data</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WomensHealth;
