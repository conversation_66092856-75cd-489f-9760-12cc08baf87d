import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Tabs,
  Tab,
  Chip,
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { motion } from 'framer-motion';
import { format } from 'date-fns';

interface VitalsData {
  date: string;
  bloodPressureSystolic: number;
  bloodPressureDiastolic: number;
  bloodSugar: number;
  weight: number;
  heartRate: number;
}

interface VitalsChartProps {
  data: VitalsData[];
}

const VitalsChart: React.FC<VitalsChartProps> = ({ data }) => {
  const [selectedTab, setSelectedTab] = React.useState(0);

  const chartConfigs = [
    {
      label: 'Blood Pressure',
      dataKeys: ['bloodPressureSystolic', 'bloodPressureDiastolic'],
      colors: ['#F44336', '#FF9800'],
      unit: 'mmHg',
      normalRange: { systolic: [90, 140], diastolic: [60, 90] },
    },
    {
      label: 'Blood Sugar',
      dataKeys: ['bloodSugar'],
      colors: ['#2196F3'],
      unit: 'mg/dL',
      normalRange: { bloodSugar: [70, 140] },
    },
    {
      label: 'Weight',
      dataKeys: ['weight'],
      colors: ['#4CAF50'],
      unit: 'kg',
      normalRange: { weight: [60, 80] },
    },
    {
      label: 'Heart Rate',
      dataKeys: ['heartRate'],
      colors: ['#9C27B0'],
      unit: 'bpm',
      normalRange: { heartRate: [60, 100] },
    },
  ];

  const currentConfig = chartConfigs[selectedTab];

  const getLatestValue = (dataKey: string) => {
    if (data.length === 0) return null;
    return data[data.length - 1][dataKey as keyof VitalsData];
  };

  const getValueStatus = (value: number, dataKey: string) => {
    const range = currentConfig.normalRange[dataKey as keyof typeof currentConfig.normalRange];
    if (!range) return 'normal';
    
    if (Array.isArray(range)) {
      if (value < range[0] || value > range[1]) return 'abnormal';
    }
    return 'normal';
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: 'white',
            p: 2,
            border: '1px solid #ccc',
            borderRadius: 1,
            boxShadow: 2,
          }}
        >
          <Typography variant="body2" fontWeight={600}>
            {format(new Date(label), 'MMM dd, yyyy')}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{ color: entry.color, mt: 0.5 }}
            >
              {entry.name}: {entry.value} {currentConfig.unit}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" fontWeight={600}>
              Health Trends
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {currentConfig.dataKeys.map((dataKey, index) => {
                const value = getLatestValue(dataKey);
                const status = value ? getValueStatus(value, dataKey) : 'normal';
                return (
                  <Chip
                    key={dataKey}
                    label={`${value || '--'} ${currentConfig.unit}`}
                    size="small"
                    color={status === 'normal' ? 'success' : 'warning'}
                    variant="outlined"
                  />
                );
              })}
            </Box>
          </Box>

          <Tabs
            value={selectedTab}
            onChange={(_, newValue) => setSelectedTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ mb: 3 }}
          >
            {chartConfigs.map((config, index) => (
              <Tab key={index} label={config.label} />
            ))}
          </Tabs>

          <Box sx={{ height: 300 }}>
            <ResponsiveContainer width="100%" height="100%">
              {currentConfig.dataKeys.length === 1 ? (
                <AreaChart data={data}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                    stroke="#666"
                  />
                  <YAxis stroke="#666" />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey={currentConfig.dataKeys[0]}
                    stroke={currentConfig.colors[0]}
                    fill={currentConfig.colors[0]}
                    fillOpacity={0.1}
                    strokeWidth={3}
                    dot={{ fill: currentConfig.colors[0], strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: currentConfig.colors[0], strokeWidth: 2 }}
                  />
                </AreaChart>
              ) : (
                <LineChart data={data}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                    stroke="#666"
                  />
                  <YAxis stroke="#666" />
                  <Tooltip content={<CustomTooltip />} />
                  {currentConfig.dataKeys.map((dataKey, index) => (
                    <Line
                      key={dataKey}
                      type="monotone"
                      dataKey={dataKey}
                      stroke={currentConfig.colors[index]}
                      strokeWidth={3}
                      dot={{ fill: currentConfig.colors[index], strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: currentConfig.colors[index], strokeWidth: 2 }}
                    />
                  ))}
                </LineChart>
              )}
            </ResponsiveContainer>
          </Box>

          {/* Legend for Blood Pressure */}
          {selectedTab === 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    backgroundColor: '#F44336',
                    borderRadius: '50%',
                    mr: 1,
                  }}
                />
                <Typography variant="caption">Systolic</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    backgroundColor: '#FF9800',
                    borderRadius: '50%',
                    mr: 1,
                  }}
                />
                <Typography variant="caption">Diastolic</Typography>
              </Box>
            </Box>
          )}

          {/* Health Insights */}
          <Box
            sx={{
              mt: 2,
              p: 2,
              backgroundColor: 'grey.50',
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'grey.200',
            }}
          >
            <Typography variant="caption" fontWeight={600} color="text.secondary">
              📊 Insight
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              {selectedTab === 0 && 'Your blood pressure has been stable over the past week. Keep monitoring regularly.'}
              {selectedTab === 1 && 'Blood sugar levels are within normal range. Continue your current diet plan.'}
              {selectedTab === 2 && 'Weight trend shows gradual improvement. Maintain your exercise routine.'}
              {selectedTab === 3 && 'Heart rate is consistent with your fitness level. Good cardiovascular health.'}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default VitalsChart;
