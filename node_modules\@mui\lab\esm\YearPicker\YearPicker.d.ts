import * as React from 'react';
type YearPickerComponent = (<TDate>(props: YearPickerProps<TDate> & React.RefAttributes<HTMLDivElement>) => React.JSX.Element) & {
  propTypes?: any;
};
/**
 * @deprecated The YearPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.
 * @ignore - do not document.
 */
declare const YearPicker: YearPickerComponent;
export default YearPicker;
export declare const yearPickerClasses: {};
export declare const getYearPickerUtilityClass: (slot: string) => string;
export type YearPickerClasses = any;
export type YearPickerClassKey = any;
export type YearPickerProps<TDate> = Record<any, any>;