"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  timelineDotClasses: true
};
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _TimelineDot.default;
  }
});
Object.defineProperty(exports, "timelineDotClasses", {
  enumerable: true,
  get: function () {
    return _timelineDotClasses.default;
  }
});
var _TimelineDot = _interopRequireDefault(require("./TimelineDot"));
var _timelineDotClasses = _interopRequireWildcard(require("./timelineDotClasses"));
Object.keys(_timelineDotClasses).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _timelineDotClasses[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _timelineDotClasses[key];
    }
  });
});