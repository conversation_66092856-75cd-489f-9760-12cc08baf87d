"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _utils = require("@mui/material/utils");
var _jsxRuntime = require("react/jsx-runtime");
/**
 * @ignore - internal component.
 */
var _default = exports.default = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
}), 'ArrowLeft');