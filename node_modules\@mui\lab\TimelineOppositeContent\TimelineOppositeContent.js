"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _clsx = _interopRequireDefault(require("clsx"));
var _styles = require("@mui/material/styles");
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _Typography = _interopRequireDefault(require("@mui/material/Typography"));
var _TimelineContext = _interopRequireDefault(require("../Timeline/TimelineContext"));
var _timelineOppositeContentClasses = require("./timelineOppositeContentClasses");
var _convertTimelinePositionToClass = _interopRequireDefault(require("../internal/convertTimelinePositionToClass"));
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = ownerState => {
  const {
    position,
    classes
  } = ownerState;
  const slots = {
    root: ['root', (0, _convertTimelinePositionToClass.default)(position)]
  };
  return (0, _composeClasses.default)(slots, _timelineOppositeContentClasses.getTimelineOppositeContentUtilityClass, classes);
};
const TimelineOppositeContentRoot = (0, _styles.styled)(_Typography.default, {
  name: 'MuiTimelineOppositeContent',
  slot: 'Root',
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[(0, _convertTimelinePositionToClass.default)(ownerState.position)]];
  }
})(({
  ownerState
}) => ({
  padding: '6px 16px',
  marginRight: 'auto',
  textAlign: 'right',
  flex: 1,
  ...(ownerState.position === 'left' && {
    textAlign: 'left'
  })
}));
const TimelineOppositeContent = /*#__PURE__*/React.forwardRef(function TimelineOppositeContent(inProps, ref) {
  const props = (0, _styles.useThemeProps)({
    props: inProps,
    name: 'MuiTimelineOppositeContent'
  });
  const {
    className,
    ...other
  } = props;
  const {
    position: positionContext
  } = React.useContext(_TimelineContext.default);
  const ownerState = {
    ...props,
    position: positionContext || 'left'
  };
  const classes = useUtilityClasses(ownerState);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(TimelineOppositeContentRoot, {
    component: "div",
    className: (0, _clsx.default)(classes.root, className),
    ownerState: ownerState,
    ref: ref,
    ...other
  });
});
process.env.NODE_ENV !== "production" ? TimelineOppositeContent.propTypes /* remove-proptypes */ = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: _propTypes.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: _propTypes.default.object,
  /**
   * @ignore
   */
  className: _propTypes.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object])
} : void 0;
TimelineOppositeContent.muiName = 'TimelineOppositeContent';
var _default = exports.default = TimelineOppositeContent;