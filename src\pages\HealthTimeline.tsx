import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Tabs,
  Tab,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Timeline as TimelineIcon,
  MonitorHeart as VitalsIcon,
  Medication as MedicationIcon,
  LocalHospital as ConditionIcon,
  TrendingUp as TrendingUpIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { format, subDays, subMonths } from 'date-fns';
import { HealthVitals, MedicalCondition, Medication, VitalsFormData } from '../types';
import Vitals<PERSON>hart from '../components/dashboard/VitalsChart';

// Validation schema for vitals form
const vitalsSchema = yup.object({
  bloodPressure: yup.object({
    systolic: yup.string().required('Systolic pressure is required'),
    diastolic: yup.string().required('Diastolic pressure is required'),
  }),
  weight: yup.string().required('Weight is required'),
  height: yup.string().required('Height is required'),
});

const HealthTimeline: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [vitalsDialogOpen, setVitalsDialogOpen] = useState(false);
  const [conditionDialogOpen, setConditionDialogOpen] = useState(false);
  const [medicationDialogOpen, setMedicationDialogOpen] = useState(false);
  const [vitalsData, setVitalsData] = useState<HealthVitals[]>([]);
  const [conditions, setConditions] = useState<MedicalCondition[]>([]);
  const [medications, setMedications] = useState<Medication[]>([]);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<VitalsFormData>({
    resolver: yupResolver(vitalsSchema),
    defaultValues: {
      bloodPressure: { systolic: '', diastolic: '' },
      bloodSugar: { fasting: '', postMeal: '', random: '' },
      weight: '',
      height: '',
      heartRate: '',
      temperature: '',
      oxygenSaturation: '',
      notes: '',
    },
  });

  // Mock data initialization
  useEffect(() => {
    const mockVitals: HealthVitals[] = [
      {
        id: '1',
        userId: 'user123',
        date: subDays(new Date(), 7),
        bloodPressure: { systolic: 120, diastolic: 80 },
        bloodSugar: { fasting: 95 },
        weight: 70.5,
        height: 175,
        bmi: 23.0,
        heartRate: 72,
        temperature: 98.6,
        oxygenSaturation: 98,
      },
      {
        id: '2',
        userId: 'user123',
        date: subDays(new Date(), 14),
        bloodPressure: { systolic: 118, diastolic: 78 },
        bloodSugar: { fasting: 92 },
        weight: 70.2,
        height: 175,
        bmi: 22.9,
        heartRate: 75,
      },
    ];

    const mockConditions: MedicalCondition[] = [
      {
        id: '1',
        name: 'Hypertension',
        diagnosedDate: subMonths(new Date(), 6),
        severity: 'mild',
        status: 'active',
        medications: ['Lisinopril'],
        notes: 'Well controlled with medication',
      },
      {
        id: '2',
        name: 'Type 2 Diabetes',
        diagnosedDate: subMonths(new Date(), 12),
        severity: 'moderate',
        status: 'active',
        medications: ['Metformin'],
        notes: 'Managing with diet and exercise',
      },
    ];

    const mockMedications: Medication[] = [
      {
        id: '1',
        name: 'Lisinopril',
        dosage: '10mg',
        frequency: 'Once daily',
        startDate: subMonths(new Date(), 6),
        prescribedBy: 'Dr. Smith',
        purpose: 'Blood pressure control',
      },
      {
        id: '2',
        name: 'Metformin',
        dosage: '500mg',
        frequency: 'Twice daily',
        startDate: subMonths(new Date(), 12),
        prescribedBy: 'Dr. Johnson',
        purpose: 'Blood sugar control',
      },
    ];

    setVitalsData(mockVitals);
    setConditions(mockConditions);
    setMedications(mockMedications);
  }, []);

  const onSubmitVitals = (data: VitalsFormData) => {
    const newVital: HealthVitals = {
      id: Date.now().toString(),
      userId: 'user123',
      date: new Date(),
      bloodPressure: {
        systolic: parseInt(data.bloodPressure.systolic),
        diastolic: parseInt(data.bloodPressure.diastolic),
      },
      bloodSugar: {
        fasting: data.bloodSugar.fasting ? parseInt(data.bloodSugar.fasting) : undefined,
        postMeal: data.bloodSugar.postMeal ? parseInt(data.bloodSugar.postMeal) : undefined,
        random: data.bloodSugar.random ? parseInt(data.bloodSugar.random) : undefined,
      },
      weight: parseFloat(data.weight),
      height: parseFloat(data.height),
      bmi: parseFloat(data.weight) / Math.pow(parseFloat(data.height) / 100, 2),
      heartRate: data.heartRate ? parseInt(data.heartRate) : undefined,
      temperature: data.temperature ? parseFloat(data.temperature) : undefined,
      oxygenSaturation: data.oxygenSaturation ? parseInt(data.oxygenSaturation) : undefined,
      notes: data.notes,
    };

    setVitalsData([...vitalsData, newVital]);
    setVitalsDialogOpen(false);
    reset();
  };

  const chartData = vitalsData.map(vital => ({
    date: format(vital.date, 'yyyy-MM-dd'),
    bloodPressureSystolic: vital.bloodPressure.systolic,
    bloodPressureDiastolic: vital.bloodPressure.diastolic,
    bloodSugar: vital.bloodSugar.fasting || vital.bloodSugar.random || 0,
    weight: vital.weight,
    heartRate: vital.heartRate || 0,
  }));

  const tabContent = [
    {
      label: 'Vitals & Measurements',
      icon: <VitalsIcon />,
      content: (
        <Box>
          <VitalsChart data={chartData} />
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Recent Vitals
              </Typography>
              <List>
                {vitalsData.slice(-5).reverse().map((vital, index) => (
                  <React.Fragment key={vital.id}>
                    <ListItem>
                      <ListItemText
                        primary={format(vital.date, 'MMMM dd, yyyy')}
                        secondary={
                          <Box>
                            <Typography variant="body2" component="span">
                              BP: {vital.bloodPressure.systolic}/{vital.bloodPressure.diastolic} mmHg
                            </Typography>
                            {vital.bloodSugar.fasting && (
                              <Typography variant="body2" component="span" sx={{ ml: 2 }}>
                                Sugar: {vital.bloodSugar.fasting} mg/dL
                              </Typography>
                            )}
                            <Typography variant="body2" component="span" sx={{ ml: 2 }}>
                              Weight: {vital.weight} kg
                            </Typography>
                            <Typography variant="body2" component="span" sx={{ ml: 2 }}>
                              BMI: {vital.bmi.toFixed(1)}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton size="small">
                          <EditIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < vitalsData.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Box>
      ),
    },
    {
      label: 'Medical Conditions',
      icon: <ConditionIcon />,
      content: (
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight={600} gutterBottom>
              Active Medical Conditions
            </Typography>
            <List>
              {conditions.map((condition, index) => (
                <React.Fragment key={condition.id}>
                  <ListItem>
                    <ListItemText
                      primary={condition.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" component="span">
                            Diagnosed: {format(condition.diagnosedDate, 'MMMM yyyy')}
                          </Typography>
                          <Box sx={{ mt: 1 }}>
                            <Chip
                              label={condition.severity}
                              size="small"
                              color={
                                condition.severity === 'mild' ? 'success' :
                                condition.severity === 'moderate' ? 'warning' : 'error'
                              }
                              sx={{ mr: 1 }}
                            />
                            <Chip
                              label={condition.status}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                          {condition.notes && (
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              {condition.notes}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < conditions.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      ),
    },
    {
      label: 'Medications',
      icon: <MedicationIcon />,
      content: (
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight={600} gutterBottom>
              Current Medications
            </Typography>
            <List>
              {medications.map((medication, index) => (
                <React.Fragment key={medication.id}>
                  <ListItem>
                    <ListItemText
                      primary={medication.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" component="span">
                            {medication.dosage} - {medication.frequency}
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 0.5 }}>
                            Prescribed by: {medication.prescribedBy}
                          </Typography>
                          <Typography variant="body2">
                            Purpose: {medication.purpose}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Started: {format(medication.startDate, 'MMMM dd, yyyy')}
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < medications.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      ),
    },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" fontWeight={700} gutterBottom>
              Health Timeline
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Track your health journey with detailed vitals and medical history
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<CalendarIcon />}
              onClick={() => {/* Open date range picker */}}
            >
              Last 3 Months
            </Button>
          </Box>
        </Box>
      </motion.div>

      <Card sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(_, newValue) => setSelectedTab(newValue)}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {tabContent.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Card>

      <motion.div
        key={selectedTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {tabContent[selectedTab].content}
      </motion.div>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 24, right: 24 }}
        onClick={() => {
          if (selectedTab === 0) setVitalsDialogOpen(true);
          else if (selectedTab === 1) setConditionDialogOpen(true);
          else if (selectedTab === 2) setMedicationDialogOpen(true);
        }}
      >
        <AddIcon />
      </Fab>

      {/* Add Vitals Dialog */}
      <Dialog
        open={vitalsDialogOpen}
        onClose={() => setVitalsDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Vitals</DialogTitle>
        <form onSubmit={handleSubmit(onSubmitVitals)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Controller
                  name="bloodPressure.systolic"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Systolic BP"
                      type="number"
                      fullWidth
                      error={!!errors.bloodPressure?.systolic}
                      helperText={errors.bloodPressure?.systolic?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="bloodPressure.diastolic"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Diastolic BP"
                      type="number"
                      fullWidth
                      error={!!errors.bloodPressure?.diastolic}
                      helperText={errors.bloodPressure?.diastolic?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="bloodSugar.fasting"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Fasting Sugar (mg/dL)"
                      type="number"
                      fullWidth
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="bloodSugar.postMeal"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Post-meal Sugar (mg/dL)"
                      type="number"
                      fullWidth
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="weight"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Weight (kg)"
                      type="number"
                      fullWidth
                      error={!!errors.weight}
                      helperText={errors.weight?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="height"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Height (cm)"
                      type="number"
                      fullWidth
                      error={!!errors.height}
                      helperText={errors.height?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="heartRate"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Heart Rate (bpm)"
                      type="number"
                      fullWidth
                    />
                  )}
                />
              </Grid>
              <Grid item xs={6}>
                <Controller
                  name="temperature"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Temperature (°F)"
                      type="number"
                      fullWidth
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Notes"
                      multiline
                      rows={3}
                      fullWidth
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setVitalsDialogOpen(false)}>Cancel</Button>
            <Button type="submit" variant="contained">Save Vitals</Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default HealthTimeline;
