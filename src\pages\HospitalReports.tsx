import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
} from '@mui/material';
import {
  LocalHospital as HospitalIcon,
  Person as DoctorIcon,
  Receipt as BillIcon,
  Assignment as ReportIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
  Sync as SyncIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { format, subDays, subMonths } from 'date-fns';
import { HospitalVisit, LabReport, MedicalBill } from '../types';

const HospitalReports: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [visitDialogOpen, setVisitDialogOpen] = useState(false);
  const [selectedVisit, setSelectedVisit] = useState<HospitalVisit | null>(null);
  const [hospitalVisits, setHospitalVisits] = useState<HospitalVisit[]>([]);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');

  // Mock data initialization
  useEffect(() => {
    const mockVisits: HospitalVisit[] = [
      {
        id: '1',
        userId: 'user123',
        hospitalName: 'AIIMS New Delhi',
        hospitalId: 'AIIMS001',
        doctorName: 'Dr. Sarah Wilson',
        doctorSpecialty: 'Cardiology',
        visitDate: subDays(new Date(), 7),
        visitType: 'consultation',
        diagnosis: ['Hypertension', 'Chest pain evaluation'],
        symptoms: ['Chest discomfort', 'Shortness of breath'],
        treatment: 'Medication adjustment and lifestyle counseling',
        prescriptions: [
          {
            id: '1',
            name: 'Lisinopril',
            dosage: '10mg',
            frequency: 'Once daily',
            startDate: subDays(new Date(), 7),
            prescribedBy: 'Dr. Sarah Wilson',
            purpose: 'Blood pressure control',
          },
        ],
        labReports: [
          {
            id: '1',
            testName: 'Lipid Profile',
            testDate: subDays(new Date(), 7),
            results: [
              { parameter: 'Total Cholesterol', value: '180', unit: 'mg/dL', normalRange: '<200', status: 'normal' },
              { parameter: 'LDL', value: '110', unit: 'mg/dL', normalRange: '<100', status: 'high' },
              { parameter: 'HDL', value: '45', unit: 'mg/dL', normalRange: '>40', status: 'normal' },
              { parameter: 'Triglycerides', value: '120', unit: 'mg/dL', normalRange: '<150', status: 'normal' },
            ],
            labName: 'AIIMS Pathology Lab',
          },
        ],
        bills: [
          {
            id: '1',
            billNumber: 'AIIMS-2024-001',
            date: subDays(new Date(), 7),
            hospitalName: 'AIIMS New Delhi',
            totalAmount: 2500,
            paidAmount: 2500,
            pendingAmount: 0,
            items: [
              { description: 'Consultation Fee', quantity: 1, rate: 500, amount: 500 },
              { description: 'ECG', quantity: 1, rate: 300, amount: 300 },
              { description: 'Lipid Profile', quantity: 1, rate: 800, amount: 800 },
              { description: 'Medicines', quantity: 1, rate: 900, amount: 900 },
            ],
            paymentStatus: 'paid',
            insuranceClaimed: true,
          },
        ],
        notes: 'Patient responded well to treatment. Follow-up in 4 weeks.',
        followUpDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
      },
      {
        id: '2',
        userId: 'user123',
        hospitalName: 'Max Healthcare',
        hospitalId: 'MAX001',
        doctorName: 'Dr. Rajesh Kumar',
        doctorSpecialty: 'Endocrinology',
        visitDate: subMonths(new Date(), 1),
        visitType: 'follow-up',
        diagnosis: ['Type 2 Diabetes Mellitus'],
        symptoms: ['Increased thirst', 'Frequent urination'],
        treatment: 'Diabetes management and dietary counseling',
        prescriptions: [
          {
            id: '2',
            name: 'Metformin',
            dosage: '500mg',
            frequency: 'Twice daily',
            startDate: subMonths(new Date(), 1),
            prescribedBy: 'Dr. Rajesh Kumar',
            purpose: 'Blood sugar control',
          },
        ],
        labReports: [
          {
            id: '2',
            testName: 'HbA1c',
            testDate: subMonths(new Date(), 1),
            results: [
              { parameter: 'HbA1c', value: '7.2', unit: '%', normalRange: '<7.0', status: 'high' },
              { parameter: 'Fasting Glucose', value: '125', unit: 'mg/dL', normalRange: '70-100', status: 'high' },
            ],
            labName: 'Max Pathology Lab',
          },
        ],
        bills: [
          {
            id: '2',
            billNumber: 'MAX-2024-002',
            date: subMonths(new Date(), 1),
            hospitalName: 'Max Healthcare',
            totalAmount: 3200,
            paidAmount: 1600,
            pendingAmount: 1600,
            items: [
              { description: 'Consultation Fee', quantity: 1, rate: 800, amount: 800 },
              { description: 'HbA1c Test', quantity: 1, rate: 1200, amount: 1200 },
              { description: 'Medicines', quantity: 1, rate: 1200, amount: 1200 },
            ],
            paymentStatus: 'partial',
            insuranceClaimed: false,
          },
        ],
        notes: 'HbA1c levels need improvement. Dietary modifications recommended.',
        followUpDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
      },
    ];

    setHospitalVisits(mockVisits);
  }, []);

  const handleSync = async () => {
    setSyncStatus('syncing');
    // Simulate API call to sync hospital data
    await new Promise(resolve => setTimeout(resolve, 3000));
    setSyncStatus('success');
    setTimeout(() => setSyncStatus('idle'), 2000);
  };

  const getVisitTypeColor = (type: string) => {
    switch (type) {
      case 'emergency': return 'error';
      case 'consultation': return 'primary';
      case 'follow-up': return 'info';
      case 'surgery': return 'warning';
      case 'checkup': return 'success';
      default: return 'default';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'error';
      case 'partial': return 'warning';
      default: return 'default';
    }
  };

  const getLabResultColor = (status: string) => {
    switch (status) {
      case 'normal': return 'success';
      case 'high': return 'error';
      case 'low': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  const totalBills = hospitalVisits.reduce((sum, visit) =>
    sum + visit.bills.reduce((billSum, bill) => billSum + bill.totalAmount, 0), 0
  );

  const pendingAmount = hospitalVisits.reduce((sum, visit) =>
    sum + visit.bills.reduce((billSum, bill) => billSum + bill.pendingAmount, 0), 0
  );

  const upcomingFollowUps = hospitalVisits.filter(visit =>
    visit.followUpDate && visit.followUpDate > new Date()
  ).length;

  const tabContent = [
    {
      label: 'Visit History',
      content: (
        <Box>
          {hospitalVisits.map((visit, index) => (
            <motion.div
              key={visit.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <HospitalIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight={600}>
                          {visit.hospitalName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {format(visit.visitDate, 'MMMM dd, yyyy')}
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Chip
                        label={visit.visitType}
                        color={getVisitTypeColor(visit.visitType)}
                        size="small"
                      />
                      <Button
                        size="small"
                        startIcon={<ViewIcon />}
                        onClick={() => {
                          setSelectedVisit(visit);
                          setVisitDialogOpen(true);
                        }}
                      >
                        View Details
                      </Button>
                    </Box>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                        Doctor Information
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <DoctorIcon sx={{ mr: 1, fontSize: 16 }} />
                        <Typography variant="body2">
                          {visit.doctorName} - {visit.doctorSpecialty}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                        Diagnosis
                      </Typography>
                      <Box>
                        {visit.diagnosis.map((diag, diagIndex) => (
                          <Chip
                            key={diagIndex}
                            label={diag}
                            size="small"
                            sx={{ mr: 1, mb: 1 }}
                          />
                        ))}
                      </Box>
                    </Grid>

                    <Grid item xs={12}>
                      <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                        Treatment Summary
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {visit.treatment}
                      </Typography>
                    </Grid>

                    {visit.followUpDate && visit.followUpDate > new Date() && (
                      <Grid item xs={12}>
                        <Alert severity="info" sx={{ mt: 1 }}>
                          <Typography variant="body2">
                            <strong>Follow-up scheduled:</strong> {format(visit.followUpDate, 'MMMM dd, yyyy')}
                          </Typography>
                        </Alert>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </Box>
      ),
    },
    {
      label: 'Lab Reports',
      content: (
        <Box>
          {hospitalVisits.map((visit) =>
            visit.labReports.map((report, index) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" fontWeight={600}>
                          {report.testName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {report.labName} • {format(report.testDate, 'MMMM dd, yyyy')}
                        </Typography>
                      </Box>
                      <Button startIcon={<DownloadIcon />} size="small">
                        Download
                      </Button>
                    </Box>

                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Parameter</TableCell>
                            <TableCell align="right">Value</TableCell>
                            <TableCell align="right">Normal Range</TableCell>
                            <TableCell align="center">Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {report.results.map((result, resultIndex) => (
                            <TableRow key={resultIndex}>
                              <TableCell>{result.parameter}</TableCell>
                              <TableCell align="right">
                                {result.value} {result.unit}
                              </TableCell>
                              <TableCell align="right">{result.normalRange}</TableCell>
                              <TableCell align="center">
                                <Chip
                                  label={result.status}
                                  size="small"
                                  color={getLabResultColor(result.status)}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </Box>
      ),
    },
    {
      label: 'Bills & Payments',
      content: (
        <Box>
          {hospitalVisits.map((visit) =>
            visit.bills.map((bill, index) => (
              <motion.div
                key={bill.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" fontWeight={600}>
                          Bill #{bill.billNumber}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {bill.hospitalName} • {format(bill.date, 'MMMM dd, yyyy')}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                        <Chip
                          label={bill.paymentStatus}
                          color={getPaymentStatusColor(bill.paymentStatus)}
                          size="small"
                        />
                        {bill.insuranceClaimed && (
                          <Chip label="Insurance Claimed" color="success" size="small" />
                        )}
                        <Button startIcon={<DownloadIcon />} size="small">
                          Download
                        </Button>
                      </Box>
                    </Box>

                    <Grid container spacing={3}>
                      <Grid item xs={12} md={8}>
                        <TableContainer component={Paper} variant="outlined">
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Description</TableCell>
                                <TableCell align="right">Qty</TableCell>
                                <TableCell align="right">Rate</TableCell>
                                <TableCell align="right">Amount</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {bill.items.map((item, itemIndex) => (
                                <TableRow key={itemIndex}>
                                  <TableCell>{item.description}</TableCell>
                                  <TableCell align="right">{item.quantity}</TableCell>
                                  <TableCell align="right">₹{item.rate}</TableCell>
                                  <TableCell align="right">₹{item.amount}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                            Payment Summary
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Total Amount:</Typography>
                            <Typography variant="body2" fontWeight={600}>₹{bill.totalAmount}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Paid Amount:</Typography>
                            <Typography variant="body2" color="success.main">₹{bill.paidAmount}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="body2">Pending:</Typography>
                            <Typography variant="body2" color="error.main">₹{bill.pendingAmount}</Typography>
                          </Box>
                          {bill.pendingAmount > 0 && (
                            <Button variant="contained" fullWidth size="small">
                              Pay Now
                            </Button>
                          )}
                        </Paper>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </Box>
      ),
    },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" fontWeight={700} gutterBottom>
              Hospital Reports
            </Typography>
            <Typography variant="body1" color="text.secondary">
              View your complete medical history, lab reports, and billing information
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={syncStatus === 'syncing' ? <LinearProgress sx={{ width: 20 }} /> : <SyncIcon />}
            onClick={handleSync}
            disabled={syncStatus === 'syncing'}
          >
            {syncStatus === 'syncing' ? 'Syncing...' : 'Sync Data'}
          </Button>
        </Box>
      </motion.div>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 1 }}>
                <HospitalIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {hospitalVisits.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Visits
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 1 }}>
                <MoneyIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                ₹{totalBills.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Bills
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'error.main', mx: 'auto', mb: 1 }}>
                <WarningIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                ₹{pendingAmount.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Amount
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 1 }}>
                <ScheduleIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {upcomingFollowUps}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Follow-ups Due
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sync Status Alert */}
      {syncStatus === 'success' && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Hospital data synchronized successfully!
        </Alert>
      )}

      {syncStatus === 'error' && (
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to sync hospital data. Please try again.
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={selectedTab}
          onChange={(_, newValue) => setSelectedTab(newValue)}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {tabContent.map((tab, index) => (
            <Tab key={index} label={tab.label} />
          ))}
        </Tabs>
      </Card>

      <motion.div
        key={selectedTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mt: 3 }}>
          {tabContent[selectedTab].content}
        </Box>
      </motion.div>

      {/* Visit Details Dialog */}
      <Dialog
        open={visitDialogOpen}
        onClose={() => setVisitDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Visit Details - {selectedVisit?.hospitalName}
        </DialogTitle>
        <DialogContent>
          {selectedVisit && (
            <Box>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" fontWeight={600}>Date:</Typography>
                  <Typography variant="body2">{format(selectedVisit.visitDate, 'MMMM dd, yyyy')}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" fontWeight={600}>Doctor:</Typography>
                  <Typography variant="body2">{selectedVisit.doctorName}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" fontWeight={600}>Specialty:</Typography>
                  <Typography variant="body2">{selectedVisit.doctorSpecialty}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" fontWeight={600}>Visit Type:</Typography>
                  <Chip label={selectedVisit.visitType} size="small" />
                </Grid>
              </Grid>

              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" fontWeight={600}>Diagnosis & Treatment</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="subtitle2" fontWeight={600} gutterBottom>Diagnosis:</Typography>
                  <Box sx={{ mb: 2 }}>
                    {selectedVisit.diagnosis.map((diag, index) => (
                      <Chip key={index} label={diag} size="small" sx={{ mr: 1, mb: 1 }} />
                    ))}
                  </Box>
                  <Typography variant="subtitle2" fontWeight={600} gutterBottom>Symptoms:</Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>{selectedVisit.symptoms.join(', ')}</Typography>
                  <Typography variant="subtitle2" fontWeight={600} gutterBottom>Treatment:</Typography>
                  <Typography variant="body2">{selectedVisit.treatment}</Typography>
                </AccordionDetails>
              </Accordion>

              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" fontWeight={600}>Prescriptions</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <List dense>
                    {selectedVisit.prescriptions.map((prescription, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={`${prescription.name} - ${prescription.dosage}`}
                          secondary={`${prescription.frequency} • ${prescription.purpose}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>

              {selectedVisit.notes && (
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" fontWeight={600}>Doctor's Notes</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2">{selectedVisit.notes}</Typography>
                  </AccordionDetails>
                </Accordion>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setVisitDialogOpen(false)}>Close</Button>
          <Button variant="contained" startIcon={<DownloadIcon />}>
            Download Report
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HospitalReports;
