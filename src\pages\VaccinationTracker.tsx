import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,

  LinearProgress,
  Fab,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab';
import {
  Vaccines as VaccineIcon,
  Add as AddIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Download as DownloadIcon,
  Notification as NotificationIcon,
  Baby as BabyIcon,
  Person as PersonIcon,
  Elderly as ElderlyIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { format, addDays, addMonths, addYears, isBefore, isAfter } from 'date-fns';
import { Vaccination } from '../types';

const VaccinationTracker: React.FC = () => {
  const [vaccinations, setVaccinations] = useState<Vaccination[]>([]);
  const [addVaccineDialogOpen, setAddVaccineDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'child' | 'adult' | 'elderly'>('all');

  // Mock vaccination data
  useEffect(() => {
    const mockVaccinations: Vaccination[] = [
      {
        id: '1',
        vaccineName: 'COVID-19 (Covishield)',
        vaccineType: 'mRNA',
        administeredDate: new Date('2023-06-15'),
        nextDueDate: new Date('2024-06-15'),
        batchNumber: 'COV123456',
        manufacturer: 'Serum Institute',
        administeredBy: 'Dr. Sharma',
        location: 'Primary Health Center, Delhi',
        sideEffects: ['Mild fever', 'Soreness at injection site'],
      },
      {
        id: '2',
        vaccineName: 'Influenza (Flu Shot)',
        vaccineType: 'Inactivated',
        administeredDate: new Date('2023-10-01'),
        nextDueDate: new Date('2024-10-01'),
        batchNumber: 'FLU789012',
        manufacturer: 'Sanofi',
        administeredBy: 'Nurse Priya',
        location: 'Apollo Hospital, Delhi',
      },
      {
        id: '3',
        vaccineName: 'Hepatitis B',
        vaccineType: 'Recombinant',
        administeredDate: new Date('2022-03-20'),
        batchNumber: 'HEP345678',
        manufacturer: 'GSK',
        administeredBy: 'Dr. Kumar',
        location: 'AIIMS, New Delhi',
      },
      {
        id: '4',
        vaccineName: 'Tetanus-Diphtheria (Td)',
        vaccineType: 'Toxoid',
        administeredDate: new Date('2021-08-10'),
        nextDueDate: new Date('2031-08-10'),
        batchNumber: 'TD901234',
        manufacturer: 'Bharat Biotech',
        administeredBy: 'Dr. Patel',
        location: 'Government Hospital, Delhi',
      },
    ];

    setVaccinations(mockVaccinations);
  }, []);

  // Vaccination schedule recommendations
  const vaccinationSchedule = [
    {
      category: 'Adult (18-64 years)',
      vaccines: [
        { name: 'COVID-19', frequency: 'Annual booster', priority: 'high' },
        { name: 'Influenza', frequency: 'Annual', priority: 'high' },
        { name: 'Tetanus-Diphtheria', frequency: 'Every 10 years', priority: 'medium' },
        { name: 'Hepatitis B', frequency: 'One-time series', priority: 'medium' },
        { name: 'HPV', frequency: 'One-time series (if not received)', priority: 'medium' },
      ],
    },
    {
      category: 'Elderly (65+ years)',
      vaccines: [
        { name: 'COVID-19', frequency: 'Annual booster', priority: 'high' },
        { name: 'Influenza', frequency: 'Annual', priority: 'high' },
        { name: 'Pneumococcal', frequency: 'One-time', priority: 'high' },
        { name: 'Shingles (Zoster)', frequency: 'One-time', priority: 'medium' },
        { name: 'Tetanus-Diphtheria', frequency: 'Every 10 years', priority: 'medium' },
      ],
    },
    {
      category: 'Travel Vaccines',
      vaccines: [
        { name: 'Yellow Fever', frequency: 'As needed for travel', priority: 'medium' },
        { name: 'Japanese Encephalitis', frequency: 'As needed for travel', priority: 'medium' },
        { name: 'Typhoid', frequency: 'As needed for travel', priority: 'low' },
        { name: 'Meningococcal', frequency: 'As needed for travel', priority: 'low' },
      ],
    },
  ];

  const getVaccineStatus = (vaccination: Vaccination) => {
    if (!vaccination.nextDueDate) return 'completed';

    const today = new Date();
    const dueDate = vaccination.nextDueDate;
    const warningDate = addDays(dueDate, -30); // 30 days before due

    if (isBefore(today, warningDate)) return 'current';
    if (isBefore(today, dueDate)) return 'due_soon';
    return 'overdue';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current': return 'success';
      case 'due_soon': return 'warning';
      case 'overdue': return 'error';
      case 'completed': return 'info';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'current': return <CheckIcon />;
      case 'due_soon': return <ScheduleIcon />;
      case 'overdue': return <WarningIcon />;
      case 'completed': return <CheckIcon />;
      default: return <VaccineIcon />;
    }
  };

  const upcomingVaccinations = vaccinations.filter(v => {
    const status = getVaccineStatus(v);
    return status === 'due_soon' || status === 'overdue';
  });

  const completedVaccinations = vaccinations.filter(v => getVaccineStatus(v) === 'completed');
  const currentVaccinations = vaccinations.filter(v => getVaccineStatus(v) === 'current');

  return (
    <Box sx={{ flexGrow: 1 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" fontWeight={700} gutterBottom>
              Vaccination Tracker
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Keep track of your immunizations and stay protected
            </Typography>
          </Box>
        </Box>
      </motion.div>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 1 }}>
                <CheckIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {currentVaccinations.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Up to Date
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 1 }}>
                <ScheduleIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {upcomingVaccinations.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Due Soon
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 1 }}>
                <VaccineIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                {vaccinations.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Vaccines
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 1 }}>
                <NotificationIcon />
              </Avatar>
              <Typography variant="h4" fontWeight={700}>
                85%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Protection Score
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Upcoming Vaccinations Alert */}
      {upcomingVaccinations.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Reminder:</strong> You have {upcomingVaccinations.length} vaccination(s) due soon.
            Please schedule your appointments to stay protected.
          </Typography>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Vaccination History */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Vaccination History
              </Typography>

              <Timeline>
                {vaccinations
                  .sort((a, b) => b.administeredDate.getTime() - a.administeredDate.getTime())
                  .map((vaccination, index) => {
                    const status = getVaccineStatus(vaccination);
                    return (
                      <TimelineItem key={vaccination.id}>
                        <TimelineSeparator>
                          <TimelineDot color={getStatusColor(status)}>
                            {getStatusIcon(status)}
                          </TimelineDot>
                          {index < vaccinations.length - 1 && <TimelineConnector />}
                        </TimelineSeparator>
                        <TimelineContent>
                          <Card variant="outlined" sx={{ mb: 2 }}>
                            <CardContent sx={{ py: 2 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                                <Box>
                                  <Typography variant="subtitle1" fontWeight={600}>
                                    {vaccination.vaccineName}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    {format(vaccination.administeredDate, 'MMMM dd, yyyy')}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', gap: 1 }}>
                                  <Chip
                                    label={status.replace('_', ' ')}
                                    color={getStatusColor(status)}
                                    size="small"
                                  />
                                  <IconButton size="small">
                                    <EditIcon />
                                  </IconButton>
                                </Box>
                              </Box>

                              <Grid container spacing={2}>
                                <Grid item xs={6}>
                                  <Typography variant="caption" color="text.secondary">
                                    Administered by:
                                  </Typography>
                                  <Typography variant="body2">
                                    {vaccination.administeredBy}
                                  </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="caption" color="text.secondary">
                                    Location:
                                  </Typography>
                                  <Typography variant="body2">
                                    {vaccination.location}
                                  </Typography>
                                </Grid>
                                {vaccination.nextDueDate && (
                                  <Grid item xs={6}>
                                    <Typography variant="caption" color="text.secondary">
                                      Next due:
                                    </Typography>
                                    <Typography variant="body2">
                                      {format(vaccination.nextDueDate, 'MMMM dd, yyyy')}
                                    </Typography>
                                  </Grid>
                                )}
                                {vaccination.batchNumber && (
                                  <Grid item xs={6}>
                                    <Typography variant="caption" color="text.secondary">
                                      Batch:
                                    </Typography>
                                    <Typography variant="body2">
                                      {vaccination.batchNumber}
                                    </Typography>
                                  </Grid>
                                )}
                              </Grid>

                              {vaccination.sideEffects && vaccination.sideEffects.length > 0 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Side effects reported:
                                  </Typography>
                                  <Box sx={{ mt: 0.5 }}>
                                    {vaccination.sideEffects.map((effect, effectIndex) => (
                                      <Chip
                                        key={effectIndex}
                                        label={effect}
                                        size="small"
                                        variant="outlined"
                                        sx={{ mr: 1, mb: 0.5 }}
                                      />
                                    ))}
                                  </Box>
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    );
                  })}
              </Timeline>
            </CardContent>
          </Card>
        </Grid>

        {/* Vaccination Schedule & Recommendations */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Recommended Schedule
              </Typography>

              {vaccinationSchedule.map((category, categoryIndex) => (
                <Box key={categoryIndex} sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                    {category.category}
                  </Typography>
                  <List dense>
                    {category.vaccines.map((vaccine, vaccineIndex) => (
                      <ListItem key={vaccineIndex} sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <VaccineIcon sx={{ fontSize: 16 }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={vaccine.name}
                          secondary={vaccine.frequency}
                          primaryTypographyProps={{ variant: 'body2' }}
                          secondaryTypographyProps={{ variant: 'caption' }}
                        />
                        <ListItemSecondaryAction>
                          <Chip
                            label={vaccine.priority}
                            size="small"
                            color={
                              vaccine.priority === 'high' ? 'error' :
                              vaccine.priority === 'medium' ? 'warning' : 'default'
                            }
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                  {categoryIndex < vaccinationSchedule.length - 1 && <Divider sx={{ my: 1 }} />}
                </Box>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  fullWidth
                >
                  Download Vaccination Certificate
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<ScheduleIcon />}
                  fullWidth
                >
                  Schedule Next Vaccination
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<NotificationIcon />}
                  fullWidth
                >
                  Set Reminders
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add vaccination"
        sx={{ position: 'fixed', bottom: 24, right: 24 }}
        onClick={() => setAddVaccineDialogOpen(true)}
      >
        <AddIcon />
      </Fab>

      {/* Add Vaccination Dialog */}
      <Dialog
        open={addVaccineDialogOpen}
        onClose={() => setAddVaccineDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Vaccination</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="Vaccine Name"
                fullWidth
                placeholder="e.g., COVID-19, Influenza"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Date Administered"
                type="date"
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Next Due Date"
                type="date"
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Administered By"
                fullWidth
                placeholder="Doctor/Nurse name"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Batch Number"
                fullWidth
                placeholder="Optional"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Location"
                fullWidth
                placeholder="Hospital/Clinic name"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Side Effects (if any)"
                fullWidth
                multiline
                rows={2}
                placeholder="Describe any side effects experienced"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddVaccineDialogOpen(false)}>Cancel</Button>
          <Button variant="contained">Add Vaccination</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VaccinationTracker;
