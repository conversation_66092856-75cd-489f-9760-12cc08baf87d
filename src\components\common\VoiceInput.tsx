import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Chip,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Mic as MicIcon,
  MicOff as MicOffIcon,
  VolumeUp as VolumeUpIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

interface VoiceInputProps {
  open: boolean;
  onClose: () => void;
}

const VoiceInput: React.FC<VoiceInputProps> = ({ open, onClose }) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [language, setLanguage] = useState<'en-US' | 'hi-IN' | 'te-IN'>('en-US');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confidence, setConfidence] = useState<number>(0);

  // Mock speech recognition for demo purposes
  // In a real implementation, you would use the Web Speech API or a service like Google Speech-to-Text
  const mockRecognition = () => {
    const mockTranscripts = {
      'en-US': [
        'Add blood pressure reading 120 over 80',
        'Schedule appointment with Dr. Smith next Tuesday',
        'Record weight as 70 kilograms',
        'Show my vaccination records',
        'What is my health score today?',
      ],
      'hi-IN': [
        'मेरा ब्लड प्रेशर 120 बटा 80 दर्ज करें',
        'डॉक्टर स्मिथ के साथ अगले मंगलवार अपॉइंटमेंट बुक करें',
        'वजन 70 किलो दर्ज करें',
        'मेरे टीकाकरण रिकॉर्ड दिखाएं',
      ],
      'te-IN': [
        'నా రక్తపోటు 120 మీద 80 రికార్డ్ చేయండి',
        'డాక్టర్ స్మిత్‌తో వచ్చే మంగళవారం అపాయింట్‌మెంట్ బుక్ చేయండి',
        'బరువు 70 కిలోలు రికార్డ్ చేయండి',
        'నా వ్యాక్సినేషన్ రికార్డులు చూపించండి',
      ],
    };

    const transcripts = mockTranscripts[language];
    const randomTranscript = transcripts[Math.floor(Math.random() * transcripts.length)];
    
    return new Promise<{ transcript: string; confidence: number }>((resolve) => {
      setTimeout(() => {
        resolve({
          transcript: randomTranscript,
          confidence: Math.random() * 0.3 + 0.7, // 70-100% confidence
        });
      }, 2000 + Math.random() * 2000); // 2-4 seconds
    });
  };

  const startListening = async () => {
    setIsListening(true);
    setError(null);
    setTranscript('');
    setConfidence(0);

    try {
      const result = await mockRecognition();
      setTranscript(result.transcript);
      setConfidence(result.confidence);
    } catch (err) {
      setError('Failed to recognize speech. Please try again.');
    } finally {
      setIsListening(false);
    }
  };

  const stopListening = () => {
    setIsListening(false);
  };

  const processCommand = async () => {
    if (!transcript) return;

    setIsProcessing(true);
    
    // Mock processing delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock success response
    setIsProcessing(false);
    
    // In a real implementation, you would:
    // 1. Send the transcript to your backend
    // 2. Process the natural language command
    // 3. Execute the appropriate action
    // 4. Return success/failure response
    
    onClose();
  };

  const languageOptions = [
    { value: 'en-US', label: 'English', flag: '🇺🇸' },
    { value: 'hi-IN', label: 'हिंदी', flag: '🇮🇳' },
    { value: 'te-IN', label: 'తెలుగు', flag: '🇮🇳' },
  ];

  const getLanguageInstructions = () => {
    switch (language) {
      case 'hi-IN':
        return 'आप अपने स्वास्थ्य डेटा को अपडेट करने, अपॉइंटमेंट बुक करने, या रिपोर्ट देखने के लिए बोल सकते हैं।';
      case 'te-IN':
        return 'మీరు మీ ఆరోగ్య డేటాను అప్‌డేట్ చేయడానికి, అపాయింట్‌మెంట్‌లు బుక్ చేయడానికి లేదా రిపోర్ట్‌లను చూడటానికి మాట్లాడవచ్చు.';
      default:
        return 'You can speak to update your health data, book appointments, or view reports.';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          p: 1,
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <VolumeUpIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" fontWeight={600}>
            Voice Assistant
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {/* Language Selection */}
        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel>Language</InputLabel>
          <Select
            value={language}
            label="Language"
            onChange={(e) => setLanguage(e.target.value as any)}
          >
            {languageOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ marginRight: 8 }}>{option.flag}</span>
                  {option.label}
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Instructions */}
        <Alert severity="info" sx={{ mb: 3 }}>
          {getLanguageInstructions()}
        </Alert>

        {/* Voice Input Area */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            py: 4,
            border: '2px dashed',
            borderColor: isListening ? 'primary.main' : 'grey.300',
            borderRadius: 2,
            backgroundColor: isListening ? 'primary.main' + '05' : 'grey.50',
            transition: 'all 0.3s ease',
          }}
        >
          <AnimatePresence>
            {isListening && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 2,
                  }}
                >
                  {/* Animated pulse rings */}
                  {[1, 2, 3].map((ring) => (
                    <motion.div
                      key={ring}
                      style={{
                        position: 'absolute',
                        width: 80 + ring * 20,
                        height: 80 + ring * 20,
                        border: '2px solid',
                        borderColor: 'primary.main',
                        borderRadius: '50%',
                        opacity: 0.3,
                      }}
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.3, 0.1, 0.3],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: ring * 0.2,
                      }}
                    />
                  ))}
                </Box>
              </motion.div>
            )}
          </AnimatePresence>

          <IconButton
            onClick={isListening ? stopListening : startListening}
            disabled={isProcessing}
            sx={{
              width: 80,
              height: 80,
              backgroundColor: isListening ? 'error.main' : 'primary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: isListening ? 'error.dark' : 'primary.dark',
              },
              '&:disabled': {
                backgroundColor: 'grey.400',
              },
            }}
          >
            {isProcessing ? (
              <CircularProgress size={32} color="inherit" />
            ) : isListening ? (
              <MicOffIcon sx={{ fontSize: 32 }} />
            ) : (
              <MicIcon sx={{ fontSize: 32 }} />
            )}
          </IconButton>

          <Typography
            variant="body1"
            sx={{ mt: 2, textAlign: 'center', fontWeight: 500 }}
          >
            {isProcessing
              ? 'Processing...'
              : isListening
              ? 'Listening... Tap to stop'
              : 'Tap to start speaking'}
          </Typography>
        </Box>

        {/* Transcript Display */}
        {transcript && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Transcript:
            </Typography>
            <Box
              sx={{
                p: 2,
                backgroundColor: 'grey.100',
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'grey.300',
              }}
            >
              <Typography variant="body1">{transcript}</Typography>
              {confidence > 0 && (
                <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                  <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                    Confidence:
                  </Typography>
                  <Chip
                    label={`${Math.round(confidence * 100)}%`}
                    size="small"
                    color={confidence > 0.8 ? 'success' : confidence > 0.6 ? 'warning' : 'error'}
                  />
                </Box>
              )}
            </Box>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={processCommand}
          variant="contained"
          disabled={!transcript || isProcessing}
          startIcon={isProcessing ? <CircularProgress size={16} /> : null}
        >
          {isProcessing ? 'Processing...' : 'Execute Command'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default VoiceInput;
